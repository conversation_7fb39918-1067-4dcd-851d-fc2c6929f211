/// 退货单-单据表
model MaterialReturnSalesForm {
  // 主键 & 外键
  tenantId                 String                   @map("tenant_id") /// 租户id
  orgId                    String                   @map("org_id") /// 组织id
  id                       String                   @id @default(uuid(7)) @map("id") /// 数据id
  // 业务字段
  materialSettlementStatus MaterialSettlementStatus @default(UN_SETTLED) @map("material_settlement_status") /// 结算状态
  code                     String                   @map("code") /// 单据编码
  purchaseType             PurchaseType?            @map("purchase_type") /// 采购类型
  supplierId               String?                  @map("supplier_id") /// 供应商ID
  supplierName             String?                  @map("supplier_name") /// 供应商名称
  contractId               String?                  @map("contract_id") /// 合同ID
  contractName             String?                  @map("contract_name") /// 合同名称
  amount                   Decimal?                 @map("amount") @db.Decimal(20, 6) /// 金额
  submitStatus             SubmitStatus             @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus              AuditStatus              @default(PENDING) @map("audit_status") /// 审批状态
  year                     Int                      @map("year") /// 年
  month                    Int                      @map("month") /// 月
  day                      Int                      @map("day") /// 日
  excelFileKey             String?                  @map("excel_file_key") /// excel文件key(针式)
  qrCodeUrl                String?                  @map("qr_code_url") /// 二维码url(针式)
  excelFileKeyA4           String?                  @map("excel_file_key_a4") /// excel文件key(A4)
  qrCodeUrlA4              String?                  @map("qr_code_url_a4") /// 二维码url(A4)

  materialReturnSalesFormDetail     MaterialReturnSalesFormDetail[]
  materialReturnSalesFormAttachment MaterialReturnSalesFormAttachment[]

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_return_sales_form")
}

/// 退货单-明细表
model MaterialReturnSalesFormDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段  / 收料单或者退库单
  parentId                     String?   @map("parent_id") /// 父级ID
  returnSalesFormId            String    @map("return_sales_form_id") /// 退货单ID
  materialReceivingInventoryId String?   @map("material_receiving_inventory_id") /// 收料单库存ID
  detailCode                   String?   @map("detail_code") /// 明细的单据编码
  detailDate                   DateTime? @map("detail_date") /// 明细的单据时间
  materialId                   String    @map("material_id") /// 材料ID
  materialName                 String    @map("material_name") /// 材料名称
  materialSpec                 String    @map("material_spec") /// 材料规格
  unit                         String?   @map("unit") /// 计量单位
  inStockQuantity              Decimal?  @map("in_stock_quantity") @db.Decimal(20, 8) /// 在库数量
  inStockPrice                 Decimal?  @map("in_stock_price") @db.Decimal(20, 6) /// 在库单价
  salesReturnQuantity          Decimal?  @map("return_sales_quantity") @db.Decimal(20, 8) /// 退货数量
  salesReturnPrice             Decimal?  @map("return_sales_price") @db.Decimal(20, 6) /// 退货单价
  salesReturnAmount            Decimal?  @map("return_sales_amount") @db.Decimal(20, 6) /// 退货金额
  orderNo                      Int       @default(autoincrement()) @map("order_no") /// 排序号
  remark                       String?   @map("remark") /// 备注

  materialReturnSalesForm MaterialReturnSalesForm @relation(fields: [returnSalesFormId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_return_sales_form_detail")
}

// 退货单-附件表
model MaterialReturnSalesFormAttachment {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  returnSalesFormId String @map("return_sales_form_id") /// 退货单ID
  fileName          String @map("file_name") /// 文件名称
  fileKey           String @map("file_key") /// 文件key
  fileSize          Int    @map("file_size") /// 文件大小
  fileExt           String @map("file_ext") /// 文件后缀
  fileContentType   String @map("file_content_type") /// 文件contentType

  materialReturnFormSales MaterialReturnSalesForm @relation(fields: [returnSalesFormId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_return_sales_form_attachment")
}
