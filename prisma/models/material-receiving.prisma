/// 收料单-单据表
model MaterialReceiving {
  // 主键 & 外键
  tenantId                 String                   @map("tenant_id") /// 租户id
  orgId                    String                   @map("org_id") /// 组织id
  id                       String                   @id @default(uuid(7)) @map("id") /// 数据id
  // 业务字段
  materialSettlementStatus MaterialSettlementStatus @default(UN_SETTLED) @map("material_settlement_status") /// 采购结算状态
  projectName              String                   @map("project_name") /// 项目名称
  code                     String                   @map("code") /// 单据编码
  purchaseType             PurchaseType?            @map("purchase_type") /// 采购类型
  supplierId               String?                  @map("supplier_id") /// 供应商ID
  supplierName             String?                  @map("supplier_name") /// 供应商名称
  contractId               String?                  @map("contract_id") /// 合同ID
  contractName             String?                  @map("contract_name") /// 合同名称
  taxExcludedAmount        Decimal?                 @map("tax_excluded_amount") @db.Decimal(20, 6) /// 不含税金额
  taxIncludedAmount        Decimal?                 @map("tax_included_amount") @db.Decimal(20, 6) /// 含税金额
  submitStatus             SubmitStatus             @default(PENDING) @map("submit_status") /// 提交状态
  auditStatus              AuditStatus              @default(PENDING) @map("audit_status") /// 审批状态
  year                     Int                      @map("year") /// 年
  month                    Int                      @map("month") /// 月
  day                      Int                      @map("day") /// 日
  creator                  String                   @map("creator") /// 创建人名称
  excelFileKey             String?                  @map("excel_file_key") /// excel文件key(针式)
  qrCodeUrl                String?                  @map("qr_code_url") /// 二维码url(针式)
  excelFileKeyA4           String?                  @map("excel_file_key_a4") /// excel文件key(A4)
  qrCodeUrlA4              String?                  @map("qr_code_url_a4") /// 二维码url(A4)

  materialReceivingDetail         MaterialReceivingDetail[]
  materialReceivingIncomingDetail MaterialReceivingIncomingDetail[]
  materialReceivingAttachment     MaterialReceivingAttachment[]

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_receiving")
}

// 收料单-材料明细表
model MaterialReceivingDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  receivingId       String   @map("receiving_id") /// 收料单ID
  materialId        String   @map("material_id") /// 材料ID
  materialName      String   @map("material_name") /// 材料名称
  materialSpec      String   @map("material_spec") /// 材料规格
  qualityStandard   String?  @map("quality_standard") /// 质量标准
  unit              String?  @map("unit") /// 计量单位
  priceType         String?  @map("price_type") /// 价格类型    
  actualQuantity    Decimal? @map("actual_quantity") @db.Decimal(20, 8) /// 实收数量
  priceExcludingTax Decimal? @map("price_excluding_tax") @db.Decimal(20, 6) /// 不含税单价
  priceIncludingTax Decimal? @map("price_including_tax") @db.Decimal(20, 6) /// 含税单价
  taxExcludedAmount Decimal? @map("tax_excluded_amount") @db.Decimal(20, 6) /// 不含税金额
  taxIncludedAmount Decimal? @map("tax_included_amount") @db.Decimal(20, 6) /// 含税金额
  orderNo           Int      @default(autoincrement()) @map("order_no") /// 排序号
  remark            String?  @map("remark") /// 备注

  materialReceiving MaterialReceiving @relation(fields: [receivingId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_receiving_detail")
}

// 收料单-验收单关联表
model MaterialReceivingIncomingDetail {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  materialIncomingInspectionDetailId String @map("incoming_inspection_detail_id") /// 验收单明细ID
  materialReceivingDetailId          String @map("receiving_detail_id") /// 收料单明细ID
  receivingId                        String @default("") @map("receiving_id") /// 收料单ID

  materialReceiving MaterialReceiving @relation(fields: [receivingId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_receiving_incoming_detail")
}

// 收料单-附件表
model MaterialReceivingAttachment {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  receivingId     String @map("receiving_id") /// 收料单ID
  fileName        String @map("file_name") /// 文件名称
  fileKey         String @map("file_key") /// 文件key
  fileSize        Int    @map("file_size") /// 文件大小
  fileExt         String @map("file_ext") /// 文件后缀
  fileContentType String @map("file_content_type") /// 文件contentType

  materialReceiving MaterialReceiving @relation(fields: [receivingId], references: [id])

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_receiving_attachment")
}

// 收料库存表
model MaterialReceivingInventory {
  // 主键 & 外键
  tenantId String @map("tenant_id") /// 租户id
  orgId    String @map("org_id") /// 组织id
  id       String @id @default(uuid(7)) @map("id") /// 数据id

  // 业务字段
  materialReceivingDetailId String?  @map("receiving_detail_id") /// 收料单明细ID
  receivingId               String   @map("receiving_id") /// 收料单ID
  materialId                String   @map("material_id") /// 材料ID
  materialName              String   @map("material_name") /// 材料名称
  materialSpec              String   @map("material_spec") /// 材料规格
  unit                      String?  @map("unit") /// 单位
  price                     Decimal? @map("price") @db.Decimal(20, 6) /// 单价(取合同的发票类型，专：不含税单价，普：含税单价)
  receivingQuantity         Decimal  @map("receiving_quantity") @db.Decimal(20, 8) /// 入库数量,收料数量
  inventoryQuantity         Decimal  @map("inventory_quantity") @db.Decimal(20, 8) /// 库存数量

  // 公共字段
  isDeleted Boolean  @default(false) @map("is_deleted") /// 是否删除
  createBy  String   @default("system") @map("create_by") /// 创建人
  updateBy  String   @default("system") @map("update_by") /// 更新人
  createAt  DateTime @default(now()) @map("create_at") /// 创建时间
  updateAt  DateTime @updatedAt @map("update_at") /// 更新时间

  @@unique([tenantId, orgId, id])
  @@map("material_receiving_inventory")
}
