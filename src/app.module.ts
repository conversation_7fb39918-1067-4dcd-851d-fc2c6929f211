import { FileModule } from '@ewing/infra-cloud-sdk';
import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';

import { CatchEverythingFilter } from '@/common/filters/catch-everything.filter';
import { ConfigModule } from '@/config/config.module';
import { AuthModule } from '@/modules/auth/auth.module';
import {
  BusinessBaseInfoModule,
  BusinessCostAccountModule,
  BusinessCostSubjectModule,
  ContractTemplateModule,
  CostDictionaryModule,
  FinancialCostSubjectModule,
  MachineryDictionaryModule,
  MaterialDictionaryModule,
  ProjectBaseInfoModule,
  SupplierDirectoryModule,
  TaxrateDictionaryModule
} from '@/modules/enterprise-center';

import { ExcelModule } from './modules/excel/excel.module';
// import { SubcontractingStandardsModule } from '@/modules/subcontracting-standards/subcontracting-standards.module';
import { FileManageModule } from './modules/file-manage/file-manage.module';
import { PlatformModule } from './modules/platform/platform.module';
import {
  ConcreteSubtotalModule,
  MaterialAllocationFromModule,
  MaterialContractModule,
  materialIncomingInspectionModule,
  MaterialRealizedPaymentRecordModule,
  MaterialReceivingModule,
  MaterialReturnInventoryFormModule,
  MaterialReturnSalesModule,
  MaterialSettlementModule,
  OrgParamsModule
} from './modules/project-center';
import { MaterialRequisitionFormModule } from './modules/project-center/material-management/material-requisition-form/material-requisition-form.module';

@Module({
  imports: [
    // HttpModule.registerAsync({
    //   useFactory: () => ({
    //     timeout: 5000,
    //     maxRedirects: 5
    //   })
    // }),
    ConcreteSubtotalModule,
    PlatformModule,
    OrgParamsModule,
    MaterialSettlementModule,
    ConfigModule,
    AuthModule,
    // SubcontractingStandardsModule,
    FileManageModule,
    BusinessCostSubjectModule,
    FinancialCostSubjectModule,
    ContractTemplateModule,
    SupplierDirectoryModule,
    MaterialDictionaryModule,
    ExcelModule,
    ProjectBaseInfoModule,
    BusinessBaseInfoModule,
    TaxrateDictionaryModule,
    BusinessCostAccountModule,
    MachineryDictionaryModule,
    CostDictionaryModule,
    materialIncomingInspectionModule,
    MaterialContractModule,
    FileModule,
    MaterialReceivingModule,
    MaterialReturnSalesModule,
    MaterialAllocationFromModule,
    MaterialReturnInventoryFormModule,
    MaterialRealizedPaymentRecordModule,
    MaterialRequisitionFormModule
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: CatchEverythingFilter
    }
  ]
})
export class AppModule {}
