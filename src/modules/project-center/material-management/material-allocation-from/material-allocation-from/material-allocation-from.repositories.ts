import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma } from '@/prisma/generated';

import {
  MaterialAllocationFormResDto,
  QueryMaterialAllocationFormDto
} from './material-allocation-from.dto';

export class MaterialAllocationFormRepository {
  constructor(private readonly prisma: PrismaService) {}

  async getList(
    reqUser: IReqUser,
    query: QueryMaterialAllocationFormDto,
    prisma: PrismaService
  ): Promise<MaterialAllocationFormResDto[]> {
    const { onlyViewSelf = false } = query;
    return await prisma.$queryRaw<MaterialAllocationFormResDto[]>`
       with temp_material_categories as (
        select
          mrd.material_allocation_from_id,
          STRING_AGG(distinct
            case
              when position('|' in mdc.full_name) > 0
              then split_part(mdc.full_name, '|', 2)
              else split_part(mdc.full_name, '|', 1)
            end,
            ','
          ) filter (where mdc.full_name is not null) material_categories
        from material_allocation_from_detail mrd
        join material_dictionary_detail mdd
          on mdd.id = mrd.material_id
          and mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
        join material_dictionary_category mdc
          on mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.org_id = mdd.org_id
        where mrd.is_deleted = false
          and mrd.tenant_id = ${reqUser.tenantId}
          and mrd.org_id = ${reqUser.orgId}
        group by mrd.material_allocation_from_id
      )
      select
        mr.material_settlement_status
        ,mr.id
        ,mr.code
				,in_o.seal_name as transfer_in_project_name
				,out_o.seal_name as transfer_out_project_name
        ,tmc.material_categories
				,mr.amount
        ,u.nickname as creator
        ,mr.year
        ,mr.month
        ,mr.day
        ,mr.submit_status
        ,mr.audit_status
        ,mr.transfer_in_project_id
        ,mr.excel_file_key
        ,mr.qr_code_url
        ,mr.excel_file_key_a4
        ,mr.qr_code_url_a4
      from material_allocation_from mr
      left join temp_material_categories tmc
        on tmc.material_allocation_from_id = mr.id
      left join platform_meta."user" u
        on u.id = mr.create_by
			left join platform_meta."org" in_o
				on in_o.id = mr.transfer_in_project_id
        and in_o.tenant_id = mr.tenant_id
			left join platform_meta."org" out_o
				on out_o.id = mr.org_id
        and out_o.tenant_id = mr.tenant_id
      where mr.tenant_id = ${reqUser.tenantId}
        and mr.org_id = ${reqUser.orgId}
        and mr.is_deleted = false
        ${onlyViewSelf ? Prisma.sql`and mr.create_By = ${reqUser.id}` : Prisma.empty}
        ${query.year ? Prisma.sql`and mr.year = ${query.year}` : Prisma.empty}
        ${query.month ? Prisma.sql`and mr.month = ${query.month}` : Prisma.empty}
        ${query.day ? Prisma.sql`and mr.day = ${query.day}` : Prisma.empty}
      order by mr.code desc, mr.id desc
    `;
  }

  async getOne(id: string, reqUser: IReqUser, prisma: PrismaService) {
    const res = await prisma.$queryRaw<any[]>`
      select mr.*, o.seal_name as project_name, u.nickname as creator from material_allocation_from mr
      left join platform_meta.org o
        on o.tenant_id = mr.tenant_id
        and o.id = mr.org_id
      left join platform_meta."user" u
        on u.id = mr.create_By
      where mr.id = ${id}
        and mr.tenant_id = ${reqUser.tenantId}
        and mr.org_id = ${reqUser.orgId}
        and mr.is_deleted = false
    `;
    return res[0];
  }
}
