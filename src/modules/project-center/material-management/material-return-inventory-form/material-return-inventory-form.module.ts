import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { MaterialReturnInventoryFormController } from './material-return-inventory-form.controller';
import { ReturnInventoryFormRepository } from './repositories/return-inventory-form.repository';
import { ReturnInventoryFormDetailRepository } from './repositories/return-inventory-form-detail.repository';
import { ReturnInventoryFormService } from './services/return-inventory-form.service';
import { ReturnInventoryFormAttachmentService } from './services/return-inventory-form-attachment.service';
import { ReturnInventoryFormDetailService } from './services/return-inventory-form-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [MaterialReturnInventoryFormController],
  providers: [
    ReturnInventoryFormAttachmentService,
    ReturnInventoryFormDetailService,
    ReturnInventoryFormService,
    ReturnInventoryFormDetailRepository,
    ReturnInventoryFormRepository
  ]
})
export class MaterialReturnInventoryFormModule {}
