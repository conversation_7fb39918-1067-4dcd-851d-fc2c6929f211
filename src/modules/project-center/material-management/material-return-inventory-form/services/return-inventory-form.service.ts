import {
  BadRequestException,
  Injectable,
  InternalServerErrorException
} from '@nestjs/common';
import * as dayjs from 'dayjs';

import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ExcelService } from '@/modules/platform/excel.service';
import {
  AuditStatus,
  DetailType,
  MaterialReturnInventoryForm,
  SubmitStatus
} from '@/prisma/generated';
import { Decimal } from '@/prisma/generated/internal/prismaNamespace';

import {
  MaterialReturnInventoryFormCreateDto,
  MaterialReturnInventoryFormResDto,
  MaterialReturnInventoryFormSupplierResDto,
  MaterialReturnInventoryFormUpdateDto,
  MaterialReturnInventoryFormUpdateSubmitStatusDto,
  QueryMaterialReturnInventoryFormDto
} from '../material-return-inventory-form.dto';
import { ReturnInventoryFormRepository } from '../repositories/return-inventory-form.repository';
import { ReturnInventoryFormDetailRepository } from '../repositories/return-inventory-form-detail.repository';
import { ReturnInventoryFormDetailService } from './return-inventory-form-detail.service';

@Injectable()
export class ReturnInventoryFormService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: ReturnInventoryFormRepository,
    private readonly excelService: ExcelService,
    private readonly returnInventoryFormDetailService: ReturnInventoryFormDetailService,
    private readonly returnInventoryFormDetailRepository: ReturnInventoryFormDetailRepository
  ) {}

  // 获取时间筛选列表
  async getDateTree(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.materialReturnInventoryForm.findMany({
      distinct: ['year', 'month', 'day'],
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          count: await this.getDateCount(reqUser, time.year, time.month),
          parentId: null,
          year: time.year,
          month: time.month
        };
      }

      // 添加子级 年_月_日
      resultMap[`${time.year}_${time.month}_${time.day}`] = {
        id: `${time.year}_${time.month}_${time.day}`,
        count: await this.getDateCount(
          reqUser,
          time.year,
          time.month,
          time.day
        ),
        parentId: `${time.year}_${time.month}`,
        year: time.year,
        month: time.month,
        day: time.day
      };
    }
    return Object.values(resultMap);
  }

  async getList(
    reqUser: IReqUser,
    query: QueryMaterialReturnInventoryFormDto
  ): Promise<MaterialReturnInventoryFormResDto[]> {
    const res: MaterialReturnInventoryFormResDto[] =
      await this.repository.getList(query, reqUser);
    return res;
  }

  async delete(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 删除前校验
    await this.beforeDelete(id, reqUser);
    // 删除退货单必须返还库存
    // 查询退货单下的父级明细
    const parentDetails =
      await this.returnInventoryFormDetailService.getParentList(id, reqUser);
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReturnInventoryForm.update({
        where: {
          id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      for (const element of parentDetails) {
        await this.returnInventoryFormDetailService.delete(element.id, reqUser);
      }
      await tx.materialReturnInventoryFormAttachment.updateMany({
        where: {
          materialReversalId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
    });
    return true;
  }

  /**
   * 修改提交状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateSubmitStatus(
    req: Request,
    id: string,
    reqUser: IReqUser,
    body: MaterialReturnInventoryFormUpdateSubmitStatusDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const { submitStatus } = body;
    const data: {
      submitStatus: SubmitStatus;
      updateBy: string;
    } = {
      submitStatus,
      updateBy: userId
    };
    // 提交状态变更校验逻辑
    await this.beforeUpdateSubmitStatus(id, reqUser, submitStatus);
    await this.prisma.materialReturnInventoryForm.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: data
    });
    if (submitStatus === SubmitStatus.SUBMITTED) {
      this.generateExcelFile(req, reqUser, id, false);
      this.generateExcelFile(req, reqUser, id, true);
    }
    return true;
  }

  /**
   * 修改审批状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 审批状态变更校验逻辑
    await this.beforeUpdateAuditStatus(id, reqUser, auditStatus);
    await this.prisma.materialReturnInventoryForm.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        auditStatus,
        updateBy: userId
      }
    });
    return true;
  }

  async beforeUpdateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const { tenantId, orgId } = reqUser;
    // 查询退库单
    const data = await this.getOne(id, reqUser);
    if (
      (auditStatus === AuditStatus.PENDING ||
        auditStatus === AuditStatus.REJECTED) &&
      data.auditStatus === AuditStatus.APPROVED
    ) {
      // 校验是否被调拨单引用
      const MaterialAllocationFromRefDetailData =
        await this.prisma.materialAllocationFromDetail.findFirst({
          where: {
            detailId: id,
            detailType: DetailType.RETURN_INVENTORY,
            orgId,
            tenantId,
            isDeleted: false
          }
        });

      if (MaterialAllocationFromRefDetailData) {
        throw new BadRequestException(
          `被调拨单引用的退库单，不能${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
    }
  }

  /**
   * 提交状态变更校验逻辑
   * @param id
   * @param reqUser
   * @param submitStatus
   */
  async beforeUpdateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    submitStatus: SubmitStatus
  ) {
    if (submitStatus === SubmitStatus.SUBMITTED) {
      // 提交状态校验
      await this.beforeSubmit(id, reqUser);
    } else {
      // 取消提交状态校验
      await this.beforeUnSubmit(id, reqUser);
    }
  }

  async beforeSubmit(id: string, reqUser: IReqUser) {
    // 查询退货单是否有明细
    const detail = await this.returnInventoryFormDetailService.getList(
      id,
      reqUser
    );
    if (!detail.length) {
      throw new BadRequestException('退库单没有明细，不能提交');
    }
    // 校验审批单的必填项
    if (
      detail.find(
        (item) =>
          item.reversalQuantity === null ||
          Decimal(item.reversalQuantity).toNumber() === 0
      )
    ) {
      throw new BadRequestException('存在明细的退库数量为空,请检查');
    }
  }

  async beforeUnSubmit(id: string, reqUser: IReqUser) {
    // 查询退货单
    const materialReceiving = await this.getOne(id, reqUser);
    if (materialReceiving.auditStatus === AuditStatus.APPROVED) {
      // 已审批不可取消提交
      throw new BadRequestException('已审批的退货单，不能取消提交');
    }
  }

  /**
   * 删除前校验
   * @param id
   * @param reqUser
   */
  async beforeDelete(id: string, reqUser: IReqUser) {
    // 查询退货单
    const returnSalesForm = await this.getOne(id, reqUser);
    if (returnSalesForm.submitStatus !== SubmitStatus.PENDING) {
      throw new BadRequestException('已提交的退库单，不能删除');
    }
  }

  async getOne(id: string, reqUser: IReqUser) {
    const data = await this.prisma.materialReturnInventoryForm.findUnique({
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      }
    });
    if (!data) {
      throw new BadRequestException('退库单不存在');
    }
    return data;
  }

  // 查询时间下的数据数量
  async getDateCount(
    reqUser: IReqUser,
    year: number,
    month: number,
    day?: number
  ): Promise<number> {
    return await this.prisma.materialReturnInventoryForm.count({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        year: year,
        month: month,
        day: day ? day : undefined
      }
    });
  }

  async getSupplierList(
    reqUser: IReqUser
  ): Promise<MaterialReturnInventoryFormSupplierResDto[]> {
    return await this.repository.getSupplierList(reqUser);
  }

  async add(
    reqUser: IReqUser,
    body: MaterialReturnInventoryFormCreateDto
  ): Promise<MaterialReturnInventoryForm> {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    // 获取当退货单的最新的退货单编号
    const code = await this.getMaxCode(reqUser, year, month);
    const data = await this.prisma.materialReturnInventoryForm.create({
      data: {
        returnInventoryType: body.returnInventoryType,
        year: year,
        month: month,
        day: day,
        code: code,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    return await this.repository.getOne(data.id, reqUser);
  }

  /**
   * 编辑退库单
   * @param id
   * @param reqUser
   * @param data
   * @returns
   */
  async update(
    id: string,
    reqUser: IReqUser,
    data: MaterialReturnInventoryFormUpdateDto
  ): Promise<boolean> {
    await this.checkUpdateData(data, reqUser);
    // 查询单据收料时间
    const receiving = await this.getOne(id, reqUser);
    if (data.year && data.month && data.month !== receiving.month) {
      data.code = await this.getMaxCode(reqUser, data.year, data.month);
    }
    await this.prisma.materialReturnInventoryForm.update({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  async checkUpdateData(
    data: MaterialReturnInventoryFormUpdateDto,
    reqUser: IReqUser
  ) {
    // 获取退货单下的所有子项
    if (data.supplierId && data.year && data.month && data.day) {
      const salesDate = dayjs()
        .year(data.year)
        .month(data.month - 1)
        .date(data.day)
        .format('YYYY-MM-DD')
        .toString();
      // 查询最小时间
      const date = await this.returnInventoryFormDetailService.getEarliestTime(
        data.supplierId,
        reqUser
      );
      if (date) {
        const minDate = dayjs(date)
          .startOf('day')
          .format('YYYY-MM-DD')
          .toString();
        console.log(minDate, salesDate);
        if (salesDate < minDate)
          throw new BadRequestException(
            `退库日期不能大于领料最小日期，领料最小日期：${minDate}`
          );
        return;
      }
    }
  }

  // 获取当前退货单的最新的退库单编号
  async getMaxCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ): Promise<string> {
    const { tenantId, orgId } = reqUser;
    const code = ['退库', `${year}${String(month).padStart(2, '0')}`, '001'];
    const maxCode = await this.prisma.materialReturnInventoryForm.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        year,
        month
      },
      orderBy: {
        code: 'desc'
      }
    });
    if (maxCode?.code.split('-')[1] === code[1]) {
      const lastCode = maxCode.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }
    return code.join('-');
  }

  async generateExcelFile(
    req: Request,
    reqUser: IReqUser,
    id: string,
    isA4Page: boolean
  ) {
    // 先把excel旧的excel文件地址置空
    await this.prisma.materialReturnInventoryForm.update({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      },
      data: isA4Page ? { excelFileKeyA4: null } : { excelFileKey: null }
    });
    // 获取进场验收单数据
    const materialReturnInventoryForm =
      await this.getReturnInventoryFormInfoById(
        reqUser.tenantId,
        reqUser.orgId,
        id
      );

    const bodyData: Record<string, any> = {
      id,
      orgName: materialReturnInventoryForm.orgName,
      supplierName: materialReturnInventoryForm.supplierName,
      returnSalesCode: materialReturnInventoryForm.code,
      siteEntryDate: dayjs(
        `${materialReturnInventoryForm.year}-${String(materialReturnInventoryForm.month).padStart(2, '0')}-${String(materialReturnInventoryForm.day).padStart(2, '0')}`
      ).format('YYYY-MM-DD'),
      type: isA4Page
        ? 'materialReturnInventoryFormExportA4'
        : 'materialReturnInventoryFormExportNeedle',
      isA4Page
    };
    const res = await this.excelService.exportExcel(req, bodyData);
    if (!res.taskId) {
      throw new InternalServerErrorException('excel导出获取taskId失败');
    }

    // 使用 Promise 轮询获取 Excel 导出进度
    const data = await this.excelService.scheduleGetProgress(req, res.taskId);

    // 保存生成的excel地址
    await this.prisma.materialReturnInventoryForm.update({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      },
      data: isA4Page
        ? {
            excelFileKeyA4: data.fileKey,
            qrCodeUrlA4: data.qrCodeUrl
          }
        : {
            excelFileKey: data.fileKey,
            qrCodeUrl: data.qrCodeUrl
          }
    });
  }

  async getReturnInventoryFormInfoById(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
          select
            mr.id
            ,mr.code
            ,mr.supplier_id
            ,mr.supplier_name
            ,u.nickname as creator
            ,mr.year
            ,mr.month
            ,mr.day
            ,o.name as org_name
          from material_return_inventory_form mr
          join platform_meta.user u
            on u.id = mr.create_by
          join platform_meta.org o
            on o.tenant_id = mr.tenant_id
            and o.id = mr.org_id
          where mr.tenant_id = ${tenantId}
            and mr.org_id = ${orgId}
            and mr.id = ${id}
            and mr.is_deleted = false
        `;

    return result[0];
  }

  // 查询导出数据接口
  async getExportSheetData(
    reqUser: IReqUser,
    returnSalesFormId: string,
    isA4Page: boolean
  ) {
    // 每行显示的数据量
    const limit = isA4Page ? 14 : 4;
    const result: any[] =
      await this.returnInventoryFormDetailRepository.getList(
        returnSalesFormId,
        reqUser,
        true
      );
    let reversalAmount = Decimal(0);
    for (const item of result) {
      reversalAmount = Decimal(reversalAmount).add(
        Decimal(item.reversalAmount)
      );
      item.reversalPrice = Decimal(item.reversalPrice).toFixed(2).toString();
      item.reversalAmount = Decimal(item.reversalAmount).toFixed(2).toString();
      item.reversalQuantity = Decimal(item.reversalQuantity)
        .toFixed(2)
        .toString();
    }
    // 总数
    const total = result.length + 1;
    // 余数
    const remainder = total % limit;
    if (remainder !== 0) {
      for (let index = 0; index < limit - remainder; index++) {
        result.push({
          id: '',
          materialId: '',
          materialName: '',
          materialSpec: '',
          unit: '',
          partName: '',
          businessCostSubjectDetailName: '',
          reversalQuantity: '',
          reversalPrice: '',
          reversalAmount: '',
          remark: ''
        });
      }
    }
    result.push({
      id: '',
      materialId: '',
      materialName: '合计',
      materialSpec: '',
      unit: '',
      partName: '',
      businessCostSubjectDetailName: '',
      reversalQuantity: '',
      reversalPrice: '',
      reversalAmount: reversalAmount.toFixed(2).toString(),
      remark: ''
    });

    return { records: result };
  }
}
