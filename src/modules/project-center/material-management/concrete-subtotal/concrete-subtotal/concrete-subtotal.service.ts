import { BadRequestException, Injectable } from '@nestjs/common';

import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma } from '@/prisma/generated';
import {
  AuditStatus,
  ContractTemplateClassifyType,
  SubmitStatus
} from '@/prisma/generated';

import { ConcreteSubtotalDetaillService } from '../concrete-subtotal-detail/concrete-subtotal-detail.service';
import {
  ConcreteSubtotalUpdateAuditStatusDto,
  ConcreteSubtotalUpdateDto,
  ConcreteSubtotalUpdateSubmitStatusDto,
  QueryConcreteSubtotalDto,
  QueryContractDto,
  QuerySupplierDto
} from './concrete-subtotal.dto';
import { ConcreteSubtotalRepository } from './concrete-subtotal.repositories';

@Injectable()
export class ConcreteSubtotalService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: ConcreteSubtotalRepository,
    private readonly concreteSubtotalDetaillService: ConcreteSubtotalDetaillService
  ) {}

  /**
   * 获取合同列表
   * @param reqUser
   * @returns
   */
  async getContractList(reqUser: IReqUser, query: QueryContractDto) {
    const { tenantId, orgId } = reqUser;
    // 查询出所有商品混凝土的合同
    const contracts = await this.prisma.$queryRaw`
      SELECT
        mc.id AS "contractId",
        mc.name AS "contractName",
        mc.party_b AS "supplierId"
      FROM material_contract mc
      JOIN contract_template ct ON mc.contract_template_id = ct.id
      WHERE
        ct.classify = ${ContractTemplateClassifyType.MATERIALS_COMMERCIAL_CONCRETE}
        AND ct.is_deleted = false
        AND mc.is_deleted = false
        AND ct.tenant_id = ${tenantId}
        AND ct.org_id = ${orgId};
    `;

    return contracts;
  }

  /**
   * 获取混凝土供应商列表
   * @param reqUser
   * @returns
   */
  async getSupplierList(reqUser: IReqUser, query: QuerySupplierDto) {
    // 查询所有的混凝土供应商
    const { tenantId } = reqUser;
    // const supplierList = await this.prisma.materialContract.findMany({
    //   where: {
    //     partyB: {
    //       in: supplierIds
    //     },
    //     tenantId,
    //     isDeleted: false
    //   },
    //   select: {
    //     id: true,
    //     partyBEndName: true
    //   }
    // });

    return [];
  }

  /**
   * 获取单据列表
   * @param reqUser
   * @returns
   */
  async getList(reqUser: IReqUser, query: QueryConcreteSubtotalDto) {
    const res = await this.repository.getList(reqUser, query, this.prisma);
    return res;
  }

  /**
   * 获取时间选择器树级数据
   * @param reqUser
   * @returns
   */
  async getDateTree(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.concreteSubtotalOrder.findMany({
      distinct: ['year', 'month', 'day'],
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          count: await this.getDateCount(reqUser, time.year, time.month),
          parentId: null,
          year: time.year,
          month: time.month
        };
      }

      // 添加子级 年_月_日
      resultMap[`${time.year}_${time.month}_${time.day}`] = {
        id: `${time.year}_${time.month}_${time.day}`,
        count: await this.getDateCount(
          reqUser,
          time.year,
          time.month,
          time.day
        ),
        parentId: `${time.year}_${time.month}`,
        year: time.year,
        month: time.month,
        day: time.day
      };
    }

    return Object.values(resultMap);
  }

  /**
   * 新增商品混凝土小票单据
   * @param req
   * @param reqUser
   */
  async add(reqUser: IReqUser) {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    // 获取当前商品混凝土小票的最新的商品混凝土小票编号
    const code = await this.getMaxCode(reqUser, year, month);

    // 在事务中进行创建主单、明细、查询
    return await this.prisma.$transaction(async (tx) => {
      // 创建主单据
      const orderData = await tx.concreteSubtotalOrder.create({
        data: {
          year,
          month,
          day,
          code,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          createBy: reqUser.id,
          updateBy: reqUser.id
        }
      });
      // 创建明细
      await this.concreteSubtotalDetaillService.add(
        { concreteSubtotalOrderId: orderData.id },
        reqUser,
        tx
      );
      // 查询新建数据
      const res = await this.getOne(orderData.id, reqUser, tx);
      return res;
    });
  }

  /**
   * 编辑商品混凝土小票
   * @param id
   * @param reqUser
   * @param data
   * @returns
   */
  async update(
    id: string,
    reqUser: IReqUser,
    data: ConcreteSubtotalUpdateDto
  ): Promise<boolean> {
    await this.checkUpdateData(data, reqUser);
    // 查询单据时间
    const order = await this.getOne(id, reqUser);
    if (data.year && data.month && data.month !== order.month) {
      data.code = await this.getMaxCode(reqUser, data.year, data.month);
    }

    await this.prisma.concreteSubtotalOrder.update({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  /**
   * 编辑单据前的校验【进场日期不能小于 合同的编制日期】
   * @param reqUser
   * @param data
   * @returns
   */
  async checkUpdateData(data: ConcreteSubtotalUpdateDto, reqUser: IReqUser) {
    // 获取单据下的所有子项
    // if (data.transferInProjectId && data.year && data.month && data.day) {
    //   const salesDate = dayjs()
    //     .year(data.year)
    //     .month(data.month - 1)
    //     .date(data.day)
    //     .format('YYYY-MM-DD')
    //     .toString();
    //   // 查询最小时间
    //   const date =
    //     await this.concreteSubtotalDetaillService.getEarliestTime(reqUser);
    //   if (date) {
    //     const minDate = dayjs(date)
    //       .startOf('day')
    //       .format('YYYY-MM-DD')
    //       .toString();
    //     console.log(minDate, salesDate);
    //     if (salesDate < minDate)
    //       throw new BadRequestException(
    //         `进场日期不能大于收料最小日期，最小日期：${minDate}`
    //       );
    //     return;
    //   }
    // }
  }

  /**
   * 删除单据
   * @param id
   * @param reqUser
   */
  async delete(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 删除前校验
    await this.beforeDelete(id, reqUser);

    await this.prisma.$transaction(async (tx) => {
      // 删除单据数据
      await tx.concreteSubtotalOrder.update({
        where: {
          id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      // 删除明细数据
      await tx.concreteSubtotalDetail.updateMany({
        where: {
          concreteSubtotalOrderId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      // 删除附件数据
      await tx.concreteSubtotalAttachment.updateMany({
        where: {
          concreteSubtotalOrderId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
    });
    return true;
  }

  /**
   * 修改提交状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    body: ConcreteSubtotalUpdateSubmitStatusDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const { submitStatus } = body;
    const data: {
      submitStatus: SubmitStatus;
      updateBy: string;
    } = {
      submitStatus,
      updateBy: userId
    };
    // 提交状态变更校验逻辑
    await this.beforeUpdateSubmitStatus(id, reqUser, submitStatus);
    await this.prisma.concreteSubtotalOrder.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: data
    });
    return true;
  }

  /**
   * 修改审批状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateAuditStatus(
    id: string,
    reqUser: IReqUser,
    body: ConcreteSubtotalUpdateAuditStatusDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const { auditStatus } = body;
    // 审批状态变更校验逻辑
    await this.beforeUpdateAuditStatus(id, reqUser, auditStatus);
    await this.prisma.concreteSubtotalOrder.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        auditStatus,
        updateBy: userId
      }
    });
    return true;
  }

  /**
   * 审核状态变更校验逻辑
   * @param id
   * @param reqUser
   * @param submitStatus
   */
  async beforeUpdateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    // 查询商品混凝土小票
    const data = await this.getOne(id, reqUser);
    if (
      (auditStatus === AuditStatus.PENDING ||
        auditStatus === AuditStatus.REJECTED) &&
      data.auditStatus === AuditStatus.APPROVED
    ) {
      // 校验是否被下级引用，后面补充
      // 被下级引用的商品混凝土小票，不能撤销审批
      // throw new BadRequestException('被下级引用的商品混凝土小票，不能撤销审批');
      // throw new BadRequestException(
      //   `被商品混凝土小票引用的退库单，不能${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
      // );
    }
  }

  /**
   * 提交状态变更校验逻辑
   * @param id
   * @param reqUser
   * @param submitStatus
   */
  async beforeUpdateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    submitStatus: SubmitStatus
  ) {
    if (submitStatus === SubmitStatus.SUBMITTED) {
      // 提交状态校验
      await this.beforeSubmit(id, reqUser);
    } else {
      // 取消提交状态校验
      await this.beforeUnSubmit(id, reqUser);
    }
  }

  /**
   * 提交操作前的校验【如果已审批不可取消提交】
   * @param concreteSubtotalOrderId
   * @param reqUser
   */
  async beforeSubmit(concreteSubtotalOrderId: string, reqUser: IReqUser) {
    const detail = await this.concreteSubtotalDetaillService.getList(
      concreteSubtotalOrderId,
      reqUser
    );
    // 查询商品混凝土小票是否有明细
    if (!detail.length) {
      throw new BadRequestException('商品混凝土小票没有明细，不能提交');
    }
    const detailData = detail[0]; // 因为只有一条数据,所以取第一条即可
    if (!detailData.materialId) {
      throw new BadRequestException('商品混凝土小票没有材料，不能提交');
    }
  }

  /**
   * 取消提交操作前的校验【如果已审批不可取消提交】
   * @param id
   * @param reqUser
   */
  async beforeUnSubmit(id: string, reqUser: IReqUser) {
    // 查询商品混凝土小票
    const materialReceiving = await this.getOne(id, reqUser);
    if (materialReceiving.auditStatus === AuditStatus.APPROVED) {
      // 已审批不可取消提交
      throw new BadRequestException('已审批的商品混凝土小票，不能取消提交');
    }
  }

  /**
   * 删除前校验【如果已经提交不可删除】
   * @param id
   * @param reqUser
   */
  async beforeDelete(id: string, reqUser: IReqUser) {
    // 查询商品混凝土小票
    const returnSalesForm = await this.getOne(id, reqUser);
    if (returnSalesForm.submitStatus !== SubmitStatus.PENDING) {
      throw new BadRequestException('已提交的商品混凝土小票，不能删除');
    }
  }

  /**
   * 根据ID获取商品混凝土小票的数据【用于新增后给前端返回数据】
   * @param id
   * @param reqUser
   */
  async getOne(
    id: string,
    reqUser: IReqUser,
    tx: Prisma.TransactionClient = this.prisma
  ) {
    const data = await tx.concreteSubtotalOrder.findUnique({
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      }
    });
    if (!data) {
      throw new BadRequestException('商品混凝土小票不存在');
    }
    return data;
  }

  // 查询时间下的数据数量
  async getDateCount(
    reqUser: IReqUser,
    year: number,
    month: number,
    day?: number
  ): Promise<number> {
    return await this.prisma.concreteSubtotalOrder.count({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        year: year,
        month: month,
        day: day ? day : undefined
      }
    });
  }

  // 获取当前商品混凝土小票的最新的商品混凝土小票编号
  async getMaxCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ): Promise<string> {
    const { tenantId, orgId } = reqUser;
    const code = ['砼', `${year}${String(month).padStart(2, '0')}`, '001'];
    const maxCode = await this.prisma.concreteSubtotalOrder.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        year,
        month
      },
      orderBy: {
        code: 'desc'
      }
    });
    if (maxCode?.code.split('-')[1] === code[1]) {
      const lastCode = maxCode.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }
    return code.join('-');
  }
}
