import { ApiProperty, PickType } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsIn,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString
} from 'class-validator';

import {
  AuditStatus,
  MaterialSettlementStatus,
  SubmitStatus
} from '@/prisma/generated';

export class ConcreteSubtotalDto {
  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '单据编码' })
  @IsNotEmpty({ message: '单据编码不能为空' })
  @IsString({ message: '单据编码必须是字符串' })
  code: string;

  @ApiProperty({ description: '结算状态' })
  @IsNotEmpty({ message: '结算状态不能为空' })
  @IsIn(Object.values(MaterialSettlementStatus), {
    message: '结算状态必须是有效枚举值'
  })
  @IsString({ message: '采购结算状态必须是字符串' })
  materialSettlementStatus: MaterialSettlementStatus;

  @ApiProperty({ description: '供应商ID' })
  @IsOptional({ message: '供应商ID可以为空' })
  @IsString({ message: '供应商ID必须是字符串' })
  supplierId?: string;

  @ApiProperty({ description: '供应商名称' })
  supplierName?: string;

  @ApiProperty({ description: '合同ID' })
  @IsOptional({ message: '合同ID可以为空' })
  @IsString({ message: '合同ID必须是字符串' })
  contractId?: string;

  @ApiProperty({ description: '合同名称' })
  contractName?: string;

  @ApiProperty({ description: '浇筑部位' })
  pouringArea?: string;

  @ApiProperty({ description: '提交状态' })
  @IsNotEmpty({ message: '提交状态不能为空' })
  @IsIn(Object.values(SubmitStatus), {
    message: '提交状态必须是有效枚举值'
  })
  @IsString({ message: '提交状态必须是字符串' })
  submitStatus: SubmitStatus;

  @ApiProperty({ description: '审批状态' })
  @IsNotEmpty({ message: '审批状态不能为空' })
  @IsIn(Object.values(AuditStatus), {
    message: '审批状态必须是有效枚举值'
  })
  @IsString({ message: '审批状态必须是字符串' })
  auditStatus: AuditStatus;

  @ApiProperty({ description: '年' })
  @IsOptional({ message: '年可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '年必须是数字' })
  year?: number;

  @ApiProperty({ description: '月' })
  @IsOptional({ message: '月可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '月必须是数字' })
  month?: number;

  @ApiProperty({ description: '日' })
  @IsOptional({ message: '日可以为空' })
  @Type(() => Number)
  @IsNumber({}, { message: '日必须是数字' })
  day?: number;

  @ApiProperty({ description: '创建人名称' })
  creator: string;

  @ApiProperty({ description: '编制时间' })
  createAt: Date;
}

// 查询单据DTO
export class QueryConcreteSubtotalDto extends PickType(ConcreteSubtotalDto, [
  'year',
  'month',
  'day'
]) {
  @ApiProperty({ description: '是否仅查看自己的数据,默认为false' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  onlyViewSelf: boolean;
}

// 查询合同DTO

export class QuerySupplierDto extends PickType(ConcreteSubtotalDto, [
  'contractId'
]) {}

// 查询供应商
export class QueryContractDto extends PickType(ConcreteSubtotalDto, [
  'supplierId'
]) {}

// 列表返回结果 DTO
export class ConcreteSubtotalResDto extends ConcreteSubtotalDto {}

// 更新DTO
export class ConcreteSubtotalUpdateDto extends PickType(ConcreteSubtotalDto, [
  'supplierId',
  'supplierName',
  'contractId',
  'contractName',
  'pouringArea',
  'year',
  'month',
  'day'
]) {
  @ApiProperty({
    description: '商品混凝土小票编码'
  })
  code: string;
}

// 提交状态DTO
export class ConcreteSubtotalUpdateSubmitStatusDto extends PickType(
  ConcreteSubtotalDto,
  ['submitStatus']
) {}

// 审核状态DTO
export class ConcreteSubtotalUpdateAuditStatusDto extends PickType(
  ConcreteSubtotalDto,
  ['auditStatus']
) {}
