import { Module } from '@nestjs/common';

import { PrismaModule } from '@/common/modules/prisma/prisma.module';
import { PlatformModule } from '@/modules/platform/platform.module';

import { ConcreteSubtotalController } from './concrete-subtotal/concrete-subtotal.controller';
import { ConcreteSubtotalRepository } from './concrete-subtotal/concrete-subtotal.repositories';
import { ConcreteSubtotalService } from './concrete-subtotal/concrete-subtotal.service';
import { ConcreteSubtotalAttachmentController } from './concrete-subtotal-attachment/concrete-subtotal-attachment.controller';
import { ConcreteSubtotalAttachmentService } from './concrete-subtotal-attachment/concrete-subtotal-attachment.service';
import { ConcreteSubtotalDetailController } from './concrete-subtotal-detail/concrete-subtotal-detail.controller';
import { ConcreteSubtotalDetailRepository } from './concrete-subtotal-detail/concrete-subtotal-detail.repositories';
import { ConcreteSubtotalDetaillService } from './concrete-subtotal-detail/concrete-subtotal-detail.service';

@Module({
  imports: [PrismaModule, PlatformModule],
  controllers: [
    ConcreteSubtotalController,
    ConcreteSubtotalAttachmentController,
    ConcreteSubtotalDetailController
  ],
  providers: [
    ConcreteSubtotalRepository,
    ConcreteSubtotalDetailRepository,
    ConcreteSubtotalService,
    ConcreteSubtotalAttachmentService,
    ConcreteSubtotalDetaillService
  ]
})
export class ConcreteSubtotalModule {}
