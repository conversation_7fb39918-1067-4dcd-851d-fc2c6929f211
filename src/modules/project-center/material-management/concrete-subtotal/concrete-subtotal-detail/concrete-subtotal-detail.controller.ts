import { Body, Controller, Get, Param, Patch } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import { ConcreteSubtotalDetailUpdateDto } from './concrete-subtotal-detail.dto';
import { ConcreteSubtotalDetaillService } from './concrete-subtotal-detail.service';

@ApiTags('商品混凝土小票/明细')
@Controller('concrete-subtotal-detail')
export class ConcreteSubtotalDetailController {
  constructor(private readonly service: ConcreteSubtotalDetaillService) {}

  @ApiOperation({ summary: '查询可选的材料' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的材料成功',
    isArray: true
  })
  @Get('/choose/material/:contractId')
  async getChooseMaterialDetail(
    @ReqUser() reqUser: IReqUser,
    @Param('contractId') contractId: string
  ) {
    return await this.service.getChooseMaterialDetail(reqUser, contractId);
  }

  @ApiOperation({ summary: '查询可选的附加费' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的附加费成功',
    isArray: true
  })
  @Get('/choose/surcharge/:contractId')
  async getChooseSurchargeList(
    @ReqUser() reqUser: IReqUser,
    @Param('contractId') contractId: string
  ) {
    return await this.service.getChooseSurchargeList(reqUser, contractId);
  }

  @ApiOperation({ summary: '查询可选的成本科目' })
  @ApiResponse({
    status: 200,
    description: '获取查询可选的成本科目成功',
    isArray: true
  })
  @Get('/choose/businessCostSubject/:materialId')
  async getChooseMaterialCategory(
    @ReqUser() reqUser: IReqUser,
    @Param('materialId') materialId: string
  ) {
    return await this.service.getChooseBusinessCostSubjectList(
      reqUser,
      materialId
    );
  }

  @ApiOperation({ summary: '查询商品混凝土小票明细' })
  @ApiResponse({
    status: 200,
    description: '查询调拨单明细成功',
    // type: [],
    isArray: true
  })
  @Get('/:id')
  async getList(@Param('id') id: string, @ReqUser() reqUser: IReqUser) {
    return await this.service.getList(id, reqUser);
  }

  @ApiOperation({ summary: '修改商品混凝土小票明细' })
  @ApiResponse({
    status: 200,
    description: '商品混凝土小票明细修改成功'
  })
  @Patch('/:id')
  async add(
    @Param('id') id: string,
    @Body() body: ConcreteSubtotalDetailUpdateDto,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.update(id, body, reqUser);
  }
}
