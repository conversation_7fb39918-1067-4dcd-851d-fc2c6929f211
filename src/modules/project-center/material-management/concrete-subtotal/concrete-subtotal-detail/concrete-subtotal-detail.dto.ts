import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

// 基础DTO
export class BaseConcreteSubtotalDetailDto {
  @ApiProperty({ description: '商品混凝土单据ID' })
  @IsNotEmpty({ message: '商品混凝土单据ID不能为空' })
  @IsString({ message: '商品混凝土单据ID必须是字符串' })
  concreteSubtotalOrderId: string;

  @ApiProperty({ description: 'id' })
  @IsNotEmpty({ message: 'id不能为空' })
  @IsString({ message: 'id必须是字符串' })
  id: string;

  @ApiProperty({ description: '材料ID' })
  @IsOptional({ message: '材料ID可以为空' })
  @IsString({ message: '材料ID必须是字符串' })
  materialId?: string;

  @ApiProperty({ description: '材料名称' })
  @IsOptional({ message: '材料名称可以为空' })
  @IsString({ message: '材料名称必须是字符串' })
  materialName?: string;

  @ApiProperty({ description: '材料规格' })
  @IsOptional({ message: '材料规格可以为空' })
  @IsString({ message: '材料规格必须是字符串' })
  materialSpec?: string;

  @ApiProperty({ description: '计量单位' })
  @IsOptional({ message: '计量单位可以为空' })
  @IsString({ message: '计量单位必须是字符串' })
  unit?: string;

  @ApiProperty({ description: '浇筑方式' })
  @IsOptional({ message: '浇筑方式可以为空' })
  @IsString({ message: '浇筑方式必须是字符串' })
  pouringType?: string;

  @ApiProperty({ description: '小票张数' })
  @IsOptional({ message: '小票张数可以为空' })
  @IsNumber({}, { message: '小票张数必须是数字' })
  receiptPages: number;

  @ApiProperty({ description: '小票数量' })
  @IsOptional({ message: '小票数量可以为空' })
  @IsNumber({}, { message: '小票数量必须是数字' })
  receiptCount: number;

  @ApiProperty({ description: '扣除比例' })
  @IsOptional({ message: '扣除比例可以为空' })
  @IsNumber({}, { message: '扣除比例必须是数字' })
  deductionRate: number;

  @ApiProperty({ description: '确认数量' })
  @IsOptional({ message: '确认数量可以为空' })
  @IsNumber({}, { message: '确认数量必须是数字' })
  insureCount: number;

  @ApiProperty({ description: '成本科目ID' })
  @IsOptional({ message: '成本科目ID可以为空' })
  @IsString({ message: '成本科目ID必须是字符串' })
  businessCostSubjectId?: string;

  @ApiProperty({ description: '成本科目名称' })
  @IsOptional({ message: '成本科目名称可以为空' })
  @IsString({ message: '成本科目名称必须是字符串' })
  businessCostSubjectName?: string;

  @ApiProperty({ description: '备注' })
  @IsOptional({ message: '备注可以为空' })
  @IsString({ message: '备注必须是字符串' })
  remark?: string | null;
}

// 新增明细DTO
export class ConcreteSubtotalDetailCreateDto extends PickType(
  BaseConcreteSubtotalDetailDto,
  ['concreteSubtotalOrderId'] as const
) {}

// 修改明细DTO
export class ConcreteSubtotalDetailUpdateDto extends PickType(
  BaseConcreteSubtotalDetailDto,
  [
    'materialId',
    'materialName',
    'materialSpec',
    'unit',
    'pouringType',
    'receiptPages',
    'receiptCount',
    'deductionRate',
    'insureCount',
    'businessCostSubjectId',
    'businessCostSubjectName',
    'remark'
  ] as const
) {}
