import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  AuditStatus,
  MaterialSettlementStatus,
  SubmitStatus
} from '@/prisma/generated';

@Injectable()
export class ConcreteSubtotalDetailRepository {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 查询可选的材料分类
   * @param materialAllocationFromId
   * @param reqUser
   */
  async getChooseMaterialCategory(reqUser: IReqUser, allocationForm: any) {
    return await this.prisma.$queryRaw`
        WITH RECURSIVE temp_inventory as (
        -- 查询可用的库存（材料）
          SELECT 
            DISTINCT mdd.material_dictionary_category_id as category_id
          from material_receiving_inventory mri
          join material_dictionary_detail mdd
            on mdd.tenant_id = mri.tenant_id
            and mdd.is_deleted = false
            and mdd.id = mri.material_id
          join material_receiving mr
            on mr.tenant_id = mri.tenant_id
            and mri.receiving_id = mr.id
            and mr.org_id = mri.org_id
            and mr.is_deleted = false
            and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
            and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
            and mr.material_settlement_status::TEXT != ${MaterialSettlementStatus.SETTLED}
            and TO_DATE(CONCAT_WS('-', mr.year, mr.month, mr.day), 'YYYY-MM-DD') <= TO_DATE(${allocationForm.year + '-' + allocationForm.month + '-' + allocationForm.day}, 'YYYY-MM-DD')
          where mri.is_deleted = false
            and mri.tenant_id = ${reqUser.tenantId}
            and mri.org_id = ${reqUser.orgId}
            and mri.inventory_quantity > 0
        ), 
        -- 根据材料id去重查询分类
        temp_category  as (
          select 
            id
            ,parent_id
            ,code
            ,name
            ,type
            ,remark
            ,level
            ,sort
            ,tenant_id
          from material_dictionary_category
          where 
            tenant_id = ${reqUser.tenantId}
            and is_deleted = false
            and id in(select category_id from temp_inventory)
          
          union all 
          
          select 
            mdc.id
            ,mdc.parent_id
            ,mdc.code
            ,mdc.name
            ,mdc.type
            ,mdc.remark
            ,mdc.level
            ,mdc.sort
            ,mdc.tenant_id
          from material_dictionary_category mdc
          join temp_category t_c
            on t_c.tenant_id = mdc.tenant_id
            and mdc.id = t_c.parent_id
          where 
            mdc.is_deleted = false
        ), 
        tmp_data as(select distinct
            id
            ,parent_id
            ,code
            ,name
            ,type
            ,remark
            ,level
            ,sort
          from temp_category
        )
        select * from tmp_data
        order by level, sort
    `;
  }
}
