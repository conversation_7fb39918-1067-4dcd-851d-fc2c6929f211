import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Prisma } from '@/prisma/generated';

import {
  ConcreteSubtotalDetailCreateDto,
  ConcreteSubtotalDetailUpdateDto
} from './concrete-subtotal-detail.dto';
// import { ConcreteSubtotalDetailRepository } from './concrete-subtotal-detail.repositories';

@Injectable()
export class ConcreteSubtotalDetaillService {
  constructor(
    private readonly prisma: PrismaService
    // private readonly repository: ConcreteSubtotalDetailRepository
  ) {}

  /**
   * 查询可选的材料分类【根据合同ID】
   * @param contractId
   * @param reqUser
   */
  async getChooseMaterialDetail(reqUser: IReqUser, contractId: string) {
    return [];
  }

  /**
   * 查询可选的附加费【根据合同ID】
   * @param contractId
   * @param reqUser
   */
  async getChooseSurchargeList(reqUser: IReqUser, contractId: string) {
    return [];
  }

  /**
   * 查询可选的成本科目【根据材料ID】
   * @param materialId
   * @param reqUser
   */
  async getChooseBusinessCostSubjectList(
    reqUser: IReqUser,
    materialId: string
  ) {
    return [];
  }

  /**
   * 获取商品混凝土下的所有明细
   * @param id
   * @param reqUser
   * @returns
   */
  async getList(concreteSubtotalOrderId: string, reqUser: IReqUser) {
    const res = await this.prisma.concreteSubtotalDetail.findMany({
      where: {
        concreteSubtotalOrderId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });

    return res;
  }

  /**
   * 新增明细
   * @param materialAllocationFromId
   * @param reqUser
   */
  async add(
    body: ConcreteSubtotalDetailCreateDto,
    reqUser: IReqUser,
    tx: Prisma.TransactionClient = this.prisma
  ): Promise<any> {
    const { concreteSubtotalOrderId } = body;
    const { tenantId, orgId, id: userId } = reqUser;

    // 新增数据
    const res = await tx.concreteSubtotalDetail.create({
      data: {
        concreteSubtotalOrderId,

        tenantId,
        orgId,
        createBy: userId,
        updateBy: userId
      }
    });

    return res;
  }

  /**
   * 更新明细
   * @param materialAllocationFromId
   * @param reqUser
   */
  async update(
    id: string,
    body: ConcreteSubtotalDetailUpdateDto,
    reqUser: IReqUser
  ) {
    const {
      materialName,
      materialSpec,
      unit,
      pouringType,
      receiptPages,
      receiptCount,
      deductionRate,
      businessCostSubjectId,
      businessCostSubjectName
    } = body;
    const { tenantId, orgId, id: userId } = reqUser;

    const res = await this.prisma.concreteSubtotalDetail.update({
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      },
      data: {
        materialName,
        materialSpec,
        unit,
        pouringType,
        receiptPages,
        receiptCount,
        deductionRate,
        businessCostSubjectId,
        businessCostSubjectName,
        updateBy: userId
      }
    });

    return res;
  }

  // 删除明细【删除单据的同时需要删除明细】
  async delete(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    const res = await this.prisma.concreteSubtotalDetail.updateMany({
      where: {
        id,
        tenantId,
        orgId
      },
      data: {
        isDeleted: true
      }
    });

    return res;
  }
}
