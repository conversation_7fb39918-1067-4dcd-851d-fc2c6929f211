import { formatString, getSelectUnionPartSql } from '@ewing/infra-cloud-sdk';
import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { AuditStatus, Prisma, PrismaClient } from '@/prisma/generated';

import { UpdateReceiveDetail } from '../material-requisition-form.interface';

@Injectable()
export class RequisitionFormDetailRepository {
  constructor(private readonly prisma: PrismaService) {}

  // 获取在库材料的分类
  async selectMaterialCategoryList(
    reqUser: IReqUser,
    year: number,
    month: number,
    day: number
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      with temp_material_detail as (
        select distinct material_dictionary_category_id
        from material_dictionary_detail mdd
        where mdd.is_deleted = false
          and mdd.tenant_id = ${reqUser.tenantId}
          and mdd.id in (
            select mri.material_id
            from material_receiving_inventory mri
            join material_receiving mr
              on mr.id = mri.receiving_id
              and mr.is_deleted = false 
              and mr.tenant_id = mri.tenant_id
              and mr.org_id = mri.org_id
              and mr.audit_status = 'APPROVED'::"AuditStatus"
              and MAKE_DATE(mr.year,mr.month,mr.day)<=MAKE_DATE(${year}::int,${month}::int,${day}::int)
            where mri.is_deleted = false
              and mri.tenant_id = ${reqUser.tenantId}
              and mri.org_id = ${reqUser.orgId}
              and mri.inventory_quantity > 0
          )
      )
      ,leaf_category as (
        select distinct mdc.full_id
        from material_dictionary_category mdc
        where mdc.is_deleted = false
          and mdc.tenant_id = ${reqUser.tenantId}
          and mdc.is_leaf = true
          and mdc.id in (select material_dictionary_category_id from temp_material_detail)
      )
      select distinct
        parent.id
        ,parent.parent_id
        ,parent.code
        ,parent.name
        ,parent.type
        ,parent.remark
      from material_dictionary_category parent
      join leaf_category leaf
        on position(parent.id in leaf.full_id) > 0
      where parent.is_deleted = false
        and parent.tenant_id = ${reqUser.tenantId}
    `;

    return result;
  }

  // 获取材料明细列表
  async selectMaterialDetailList(
    reqUser: IReqUser,
    categoryId: string,
    year: number,
    month: number,
    day: number
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      with temp_category as (
        select distinct id
        from material_dictionary_category
        where is_deleted = false
          and tenant_id = ${reqUser.tenantId}
          and position(${categoryId} in full_id) > 0
      )
      select
        mdd.id
        ,mdd.name as material_name
        ,mdd.specification_model as material_spec
        ,mdd.metering_unit as unit
        ,round(sum(mri.inventory_quantity), 8) as inventory_quantity
      from material_dictionary_detail mdd
      join material_receiving_inventory mri
        on mri.is_deleted = false
        and mri.tenant_id = ${reqUser.tenantId}
        and mri.org_id = ${reqUser.orgId}
        and mri.material_id = mdd.id
      join material_receiving mr
        on mr.is_deleted = false
        and mr.tenant_id = mri.tenant_id
        and mr.org_id = mri.org_id
        and mr.id = mri.receiving_id
        and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
      where mdd.is_deleted = false
        and mdd.tenant_id = ${reqUser.tenantId}
        ${categoryId ? Prisma.sql`and mdd.material_dictionary_category_id in (select id from temp_category)` : Prisma.empty}
        and mdd.id in (
          select mri.material_id
          from material_receiving_inventory mri
          join material_receiving mr
            on mr.id = mri.receiving_id
            and mr.is_deleted = false 
            and mr.tenant_id = mri.tenant_id
            and mr.org_id = mri.org_id
            and mr.audit_status = 'APPROVED'::"AuditStatus"
            and MAKE_DATE(mr.year,mr.month,mr.day)<=MAKE_DATE(${year}::int,${month}::int,${day}::int)
          where mri.is_deleted = false
            and mri.tenant_id = ${reqUser.tenantId}
            and mri.org_id = ${reqUser.orgId}
            and mri.inventory_quantity > 0
        )
      group by
        mdd.id
        ,mdd.name
        ,mdd.specification_model
        ,mdd.metering_unit
        ,mdd.sort
      order by mdd.material_dictionary_category_id, mdd.sort
    `;

    return result;
  }

  // 获取有库存的收料信息
  async selectMaterialReceiveInfo(
    tenantId: string,
    orgId: string,
    year: number,
    month: number,
    day: number
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      with temp_material_receiving as (
        select
          mr.id
          ,mr.code
          ,mr.year
          ,mr.month
          ,mr.day
          ,TO_CHAR(MAKE_DATE(mr.year, mr.month, mr.day), 'YYYY-MM-DD') as bill_date
          ,mri.id as detail_id
          ,mri.material_id
          ,mri.material_name
          ,mri.material_spec
          ,mri.unit
          ,mri.price
          ,mri.inventory_quantity
          ,1 as type_order_no
          ,'receiving' as item_type
        from material_receiving mr
        join material_receiving_inventory mri
          on mri.is_deleted = false
          and mri.tenant_id = mr.tenant_id
          and mri.org_id = mr.org_id
          and mri.receiving_id = mr.id
          and mri.inventory_quantity > 0
        where mr.is_deleted = false
          and mr.tenant_id = ${tenantId}
          and mr.org_id = ${orgId}
          and mr.audit_status = 'APPROVED'::"AuditStatus"
          and MAKE_DATE(mr.year,mr.month,mr.day)<=MAKE_DATE(${year}::int,${month}::int,${day}::int)
      )
      ,temp_return_inventory as (
        select
          mf.id
          ,mf.code
          ,mf.year
          ,mf.month
          ,mf.day
          ,TO_CHAR(MAKE_DATE(mf.year, mf.month, mf.day), 'YYYY-MM-DD') as bill_date
          ,mfd.id as detail_id
          ,mfd.material_id
          ,mfd.material_name
          ,mfd.material_spec
          ,mfd.unit
          ,mfd.reversal_price as price
          ,mfd.reversal_inventory_quantity as inventory_quantity
          ,1 as type_order_no
          ,'reversal' as item_type
        from material_return_inventory_form mf
        join material_return_inventory_form_detail mfd
          on mfd.is_deleted = false
          and mfd.tenant_id = mf.tenant_id
          and mfd.org_id = mf.org_id
          and mfd.material_reversal_id = mf.id
          and mfd.parent_id is null
          and mfd.reversal_inventory_quantity > 0
        where mf.is_deleted = false
          and mf.tenant_id = ${tenantId}
          and mf.org_id = ${orgId}
          and mf.audit_status = 'APPROVED'::"AuditStatus"
          and MAKE_DATE(mf.year,mf.month,mf.day)<=MAKE_DATE(${year}::int,${month}::int,${day}::int)
      )
      select * from
      (
        select * from temp_material_receiving
        union all
        select * from temp_return_inventory
      ) tt
      order by type_order_no, year, month, day, id
    `;

    return result;
  }

  // 获取退库单信息
  async selectReturnInventoryInfo(
    tenantId: string,
    orgId: string,
    materialReceivingIds: string[],
    year: number,
    month: number,
    day: number
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      select
        mf.id
        ,mf.code
        ,TO_CHAR(MAKE_DATE(mf.year, mf.month, mf.day), 'YYYY-MM-DD') as return_inventory_date
        ,mfd.id as detail_id
        ,mfd.material_receiving_id
        ,mfd.material_id
        ,mfd.material_name
        ,mfd.material_spec
        ,mfd.unit
        ,mfd.reversal_price as price
        ,mfd.reversal_inventory_quantity as inventory_quantity
      from material_return_inventory_form mf
      join material_return_inventory_form_detail mfd
        on mfd.is_deleted = false
        and mfd.tenant_id = mf.tenant_id
        and mfd.org_id = mf.org_id
        and mfd.material_reversal_id = mf.id
        and mfd.material_receiving_id in (${Prisma.join(materialReceivingIds)})
        and mfd.parent_id is null
        and mfd.reversal_inventory_quantity > 0
      where mf.is_deleted = false
        and mf.tenant_id = ${tenantId}
        and mf.org_id = ${orgId}
        and mf.audit_status = 'APPROVED'::"AuditStatus"
        and MAKE_DATE(mf.year,mf.month,mf.day)<=MAKE_DATE(${year}::int,${month}::int,${day}::int)
      order by mf.year desc, mf.month desc, mf.day desc
    `;

    return result;
  }

  // 批量更新领料单剩余库存
  async bulkUpdateMaterialReceiveDetail(
    reqUser: IReqUser,
    tx: PrismaClient,
    records: UpdateReceiveDetail[]
  ) {
    const filedMap = {
      id: { type: 'string' },
      inventoryQuantity: { type: 'number' }
    };
    const tempTable = getSelectUnionPartSql(filedMap, records);

    const sql = /* sql */ `
      with temp_receiving_inventory as (${tempTable})
      update material_receiving_inventory mri
      set
        inventory_quantity = tri.inventory_quantity
        ,update_at = now()
        ,update_by = ${formatString(reqUser.id)}
      from temp_receiving_inventory tri
      where tri.id = mri.id
        and mri.is_deleted = false
        and mri.tenant_id = ${formatString(reqUser.tenantId)}
        and mri.org_id = ${formatString(reqUser.orgId)}
    `;

    await tx.$executeRawUnsafe(sql);
  }

  // 批量更新退库单剩余库存
  async bulkUpdateMaterialReturnInventoryDetail(
    reqUser: IReqUser,
    tx: PrismaClient,
    records: UpdateReceiveDetail[]
  ) {
    const filedMap = {
      id: { type: 'string' },
      inventoryQuantity: { type: 'number' }
    };
    const tempTable = getSelectUnionPartSql(filedMap, records);

    const sql = /* sql */ `
      with temp_return_inventory_form_detail as (${tempTable})
      update material_return_inventory_form_detail mfd
      set
        reversal_inventory_quantity = tfd.inventory_quantity
        ,update_at = now()
        ,update_by = ${formatString(reqUser.id)}
      from temp_return_inventory_form_detail tfd
      where tfd.id = mfd.id
        and mfd.is_deleted = false
        and mfd.tenant_id = ${formatString(reqUser.tenantId)}
        and mfd.org_id = ${formatString(reqUser.orgId)}
    `;

    await tx.$executeRawUnsafe(sql);
  }

  // 更新领料单明细-父级的单价和金额
  // 金额算法： SUM(子级金额)
  // 单价算法： SUM(子级金额) / 实收数量
  async updateParentDetailPriceAndAmount(
    reqUser: IReqUser,
    tx: PrismaClient,
    requisitionBillId: string
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    await tx.$executeRaw`
      with temp_detail as (
        select
          parent_id as detail_id
          ,SUM(amount) as amount
        from material_requisition_form_detail
        where is_deleted = false
          and tenant_id = ${tenantId}
          and org_id = ${orgId}
          and parent_id is not null
          and requisition_form_id = ${requisitionBillId}
        group by parent_id
      )
      update material_requisition_form_detail mfd
      set 
        amount = td.amount
        ,price = ROUND(td.amount/actual_quantity,6)
        ,update_at = now()
        ,update_by = ${userId}
      from temp_detail td
      where td.detail_id = mfd.id
        and mfd.is_deleted = false
        and mfd.tenant_id = ${tenantId}
        and mfd.org_id = ${orgId}
        and mfd.requisition_form_id = ${requisitionBillId}
    `;
  }

  // 删除领料单明细，返还收料单库存
  async updateMaterialReceivingDetailInventory(
    tx: PrismaClient,
    reqUser: IReqUser,
    requisitionBillId?: string,
    detailId?: string
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    await tx.$executeRaw`
      with total_change as (
        select
          mfd.material_receiving_inventory_id
          ,SUM(mfd.actual_quantity) AS total_actual_quantity
        from material_requisition_form_detail mfd
        where mfd.is_deleted = false
          and mfd.tenant_id = ${tenantId}
          and mfd.org_id = ${orgId}
          and mfd.requisition_form_id = ${requisitionBillId}
          and mfd.parent_id is not null
          ${detailId ? Prisma.sql`and mfd.parent_id = ${detailId}` : Prisma.empty}
        group by mfd.material_receiving_inventory_id
      )
      update material_receiving_inventory mri
      set
        inventory_quantity = mri.inventory_quantity + total_change.total_actual_quantity
        ,update_at = now()
        ,update_by = ${userId}
      from total_change
      where total_change.material_receiving_inventory_id = mri.id
        and mri.is_deleted = false
        and mri.tenant_id = ${tenantId}
        and mri.org_id = ${orgId}
    `;
  }

  // 删除领料单明细，返还退库单库存
  async updateMaterialReversalDetailInventory(
    tx: PrismaClient,
    reqUser: IReqUser,
    requisitionBillId?: string,
    detailId?: string
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    await tx.$executeRaw`
      with total_change as (
        select
          mfd.material_receiving_inventory_id
          ,SUM(mfd.actual_quantity) AS total_actual_quantity
        from material_requisition_form_detail mfd
        where mfd.is_deleted = false
          and mfd.tenant_id = ${tenantId}
          and mfd.org_id = ${orgId}
          and mfd.requisition_form_id = ${requisitionBillId}
          and mfd.parent_id is not null
          ${detailId ? Prisma.sql`and mfd.parent_id = ${detailId}` : Prisma.empty}
        group by mfd.material_receiving_inventory_id
      )
      update material_return_inventory_form_detail mrid
      set
        reversal_inventory_quantity = mrid.reversal_inventory_quantity + total_change.total_actual_quantity
        ,update_at = now()
        ,update_by = ${userId}
      from total_change
      where total_change.material_receiving_inventory_id = mrid.id
        and mrid.is_deleted = false
        and mrid.tenant_id = ${tenantId}
        and mrid.org_id = ${orgId}
    `;
  }

  // 获取指定材料的库存信息
  async selectMaterialInventoryInfoInfo(
    tenantId: string,
    orgId: string,
    requisitionFormId: string,
    materialId: string
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
      select
        mri.material_id
        ,ROUND(SUM(mri.inventory_quantity),6) as inventory_quantity
      from material_receiving mr
      join material_receiving_inventory mri
        on mri.is_deleted = false
        and mri.tenant_id = mr.tenant_id
        and mri.org_id = mr.org_id
        and mri.receiving_id = mr.id
        and mri.material_id = ${materialId}
      join material_requisition_form mrf
        on mrf.is_deleted = false
        and mrf.tenant_id = mr.tenant_id
        and mrf.org_id = mr.org_id
        and mrf.id = ${requisitionFormId}
      where mr.is_deleted = false
        and mr.tenant_id = ${tenantId}
        and mr.org_id = ${orgId}
        and mr.audit_status = 'APPROVED'::"AuditStatus"
        and MAKE_DATE(mr.year,mr.month,mr.day)<=MAKE_DATE(mrf.year,mrf.month,mrf.day)
      group by mri.material_id
    `;
    return result[0];
  }
}
