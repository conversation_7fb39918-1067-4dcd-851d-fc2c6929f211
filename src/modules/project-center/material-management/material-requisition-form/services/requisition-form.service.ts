import { add } from '@ewing/infra-cloud-sdk';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException
} from '@nestjs/common';
import * as dayjs from 'dayjs';

import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Utils } from '@/common/utils';
import { ExcelService } from '@/modules/platform/excel.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { AuditStatus, PrismaClient, SubmitStatus } from '@/prisma/generated';

import {
  QueryRequisitionBillListDto,
  RequisitionBillListResponseDto,
  RequisitionDepartmentListResponseDto,
  UpdateRequisitionBillDto
} from '../material-requisition-form.dto';
import { RequisitionFormRepository } from '../repositories/requisition-form.repository';
import { RequisitionFormDetailRepository } from '../repositories/requisition-form-detail.repository';

@Injectable()
export class MaterialRequisitionFormService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: RequisitionFormRepository,
    private readonly detailRepository: RequisitionFormDetailRepository,
    private readonly platformService: PlatformService,
    private readonly excelService: ExcelService
  ) {}

  // 获取时间筛选列表
  async getTimeList(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    const dates = await this.prisma.materialRequisitionForm.findMany({
      select: {
        year: true,
        month: true,
        day: true
      },
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          parentId: null,
          year: time.year,
          month: time.month,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}`].count += 1;

      // 添加子级 年_月_日
      if (!resultMap[`${time.year}_${time.month}_${time.day}`]) {
        resultMap[`${time.year}_${time.month}_${time.day}`] = {
          id: `${time.year}_${time.month}_${time.day}`,
          parentId: `${time.year}_${time.month}`,
          year: time.year,
          month: time.month,
          day: time.day,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}_${time.day}`].count += 1;
    }

    return Object.values(resultMap);
  }

  async getBillList(
    req: Request,
    reqUser: IReqUser,
    query: QueryRequisitionBillListDto
  ): Promise<RequisitionBillListResponseDto[]> {
    const bills = await this.repository.selectBillList(reqUser, query);

    // 查询供应商
    const parentOrgIds = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    const suppliers = (await this.repository.selectSupplierList(
      reqUser.tenantId,
      parentOrgIds
    )) as Array<{
      id: string;
      name: string;
    }>;
    const supplierMap = suppliers.reduce(
      (pre: Record<string, { id: string; name: string }>, cur) => {
        pre[cur.id] = cur;
        return pre;
      },
      {}
    );

    const orgs = await this.platformService.getOrgs(req, reqUser.tenantId, [
      reqUser.orgId
    ]);
    const orgName = orgs[0].sealName;
    for (const item of bills) {
      item.orgName = orgName;
      item.departmentName = supplierMap[item.supplierId]?.name;
      if (item.year && item.month && item.day) {
        // 领料日期
        item.requisitionDate = dayjs(
          `${item.year}-${item.month}-${item.day}`
        ).format('YYYY-MM-DD');
      }
    }

    return bills;
  }

  // 新增领料单
  async addRequisitionBill(
    req: Request,
    reqUser: IReqUser
  ): Promise<RequisitionBillListResponseDto> {
    // 获取进场时间
    const dateNow = dayjs();
    const year = dateNow.year();
    const month = dateNow.month() + 1;
    const day = dateNow.date();

    const code = await this.generateBillCode(reqUser, year, month);

    const data = await this.prisma.materialRequisitionForm.create({
      data: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        creator: reqUser.nickname,
        createBy: reqUser.id,
        updateBy: reqUser.id,
        code,
        year,
        month,
        day
      },
      select: {
        id: true,
        settlementStatus: true,
        code: true,
        creator: true,
        year: true,
        month: true,
        day: true,
        auditStatus: true,
        submitStatus: true,
        createAt: true,
        updateAt: true
      }
    });

    const orgs = await this.platformService.getOrgs(req, reqUser.tenantId, [
      reqUser.orgId
    ]);
    const orgName = orgs[0].sealName;
    (data as any).orgName = orgName;

    return data as RequisitionBillListResponseDto;
  }

  // 生成单据编码
  private async generateBillCode(
    reqUser: IReqUser,
    year: number,
    month: number,
    id?: string
  ) {
    const code = ['领', `${year}${String(month).padStart(2, '0')}`, '001'];
    const lastInspectionBill =
      await this.prisma.materialRequisitionForm.findFirst({
        where: Object.assign(
          {
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            isDeleted: false,
            year,
            month
          },
          id ? { id: { not: id } } : {}
        ),
        orderBy: {
          code: 'desc'
        }
      });

    if (lastInspectionBill) {
      const lastCode = lastInspectionBill.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }

    return code.join('-');
  }

  // 获取可选的领料单位
  async getRequisitionDepartmentList(
    req: Request,
    reqUser: IReqUser
  ): Promise<RequisitionDepartmentListResponseDto[]> {
    const parentOrgIds = await this.platformService.getOrgParentIds(
      req,
      reqUser.tenantId,
      reqUser.orgId
    );
    const suppliers = await this.repository.selectSupplierList(
      reqUser.tenantId,
      parentOrgIds
    );

    return suppliers as RequisitionDepartmentListResponseDto[];
  }

  // 编辑领料单
  async editRequisitionBill(
    req: Request,
    reqUser: IReqUser,
    data: UpdateRequisitionBillDto
  ) {
    // 获取原始数据
    const oldData = await this.prisma.materialRequisitionForm.findFirst({
      where: {
        id: data.id,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      select: {
        id: true,
        supplierId: true,
        auditStatus: true,
        submitStatus: true,
        partName: true,
        year: true,
        month: true,
        day: true
      }
    });

    if (!oldData) throw new BadRequestException('领料单据不存在!');

    // 是否提交数据
    const isSubmit =
      data.submitStatus === SubmitStatus.SUBMITTED &&
      data.submitStatus !== oldData.submitStatus;

    if (
      data.submitStatus === SubmitStatus.PENDING &&
      data.submitStatus !== oldData.submitStatus &&
      (oldData.auditStatus === AuditStatus.AUDITING ||
        oldData.auditStatus === AuditStatus.APPROVED)
    ) {
      throw new BadRequestException('审核中/已审核的单据不允许取消提交!');
    }

    if (
      data.auditStatus &&
      oldData.auditStatus === AuditStatus.APPROVED &&
      data.auditStatus !== AuditStatus.APPROVED
    ) {
      // @TODO:
      // const result = await this.repository.selectReceivedInspectionBillIds(
      //   reqUser.tenantId,
      //   reqUser.orgId,
      //   data.id
      // );
      // if (result.length) {
      //   throw new BadRequestException(
      //     `审批通过且被收料单引用的进场验收单，不能修改审批状态为: ${AuditStatusText[data.auditStatus]}`
      //   );
      // }
    }

    // 是否修改了领料日期
    if (
      data.year &&
      data.month &&
      data.day &&
      (data.year !== oldData.year ||
        data.month !== oldData.month ||
        data.day !== oldData.day)
    ) {
      // 改进场日期要重新生成编码
      (data as any).code = await this.generateBillCode(
        reqUser,
        data.year,
        data.month,
        data.id
      );
    }

    // 更新领料单
    await this.prisma.materialRequisitionForm.update({
      where: {
        id: data.id,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });

    if (isSubmit) {
      this.generateExcelFile(req, reqUser, data.id, false);
      this.generateExcelFile(req, reqUser, data.id, true);
    }

    return true;
  }

  // 生成领料单的excel文件
  async generateExcelFile(
    req: Request,
    reqUser: IReqUser,
    id: string,
    isA4Page: boolean // 是否A4纸张
  ) {
    // 先把excel旧的excel文件地址置空
    await this.prisma.materialRequisitionForm.update({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      },
      data: isA4Page ? { excelFileKeyA4: null } : { excelFileKey: null }
    });
    // 获取进场验收单数据
    const billInfo = await this.repository.getBillInfoById(
      reqUser.tenantId,
      reqUser.orgId,
      id
    );

    const bodyData: Record<string, any> = {
      id,
      orgName: billInfo.orgName,
      requisitionFormCode: billInfo.code,
      departmentName: billInfo.departmentName,
      partName: billInfo.partName,
      requisitionDate: billInfo.requisitionDate,
      type: isA4Page
        ? 'materialRequisitionFormExportA4'
        : 'materialRequisitionFormExportNeedle',
      isA4Page
    };
    const { taskId } = await this.excelService.exportExcel(req, bodyData);
    if (!taskId) {
      throw new InternalServerErrorException('excel导出获取taskId失败');
    }

    // 使用 Promise 轮询获取 Excel 导出进度
    const data = await this.excelService.scheduleGetProgress(req, taskId);

    // 保存生成的excel地址
    await this.prisma.materialRequisitionForm.update({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      },
      data: isA4Page
        ? {
            excelFileKeyA4: data.fileKey,
            qrCodeUrlA4: data.qrCodeUrl
          }
        : {
            excelFileKey: data.fileKey,
            qrCodeUrl: data.qrCodeUrl
          }
    });
  }

  // 删除领料单
  async deleteRequisitionBill(reqUser: IReqUser, id: string) {
    const requisitionBill = await this.prisma.materialRequisitionForm.findFirst(
      {
        where: {
          id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: {
          submitStatus: true,
          auditStatus: true
        }
      }
    );

    // 校验领料单提交状态
    if (
      requisitionBill &&
      requisitionBill.submitStatus === SubmitStatus.SUBMITTED
    ) {
      throw new BadRequestException('已提交的领料单不允许删除!');
    }

    await this.prisma.$transaction(async (tx) => {
      // 删除单据
      await tx.materialRequisitionForm.updateMany({
        where: {
          id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });

      // 删除领料单明细，需要返还收料单和退库单的库存
      await this.detailRepository.updateMaterialReceivingDetailInventory(
        tx as PrismaClient,
        reqUser,
        id
      );
      await this.detailRepository.updateMaterialReversalDetailInventory(
        tx as PrismaClient,
        reqUser,
        id
      );

      // 删除明细
      await tx.materialRequisitionFormDetail.updateMany({
        where: {
          requisitionFormId: id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });

      // 删除附件
      await tx.materialRequisitionFormAttachment.updateMany({
        where: {
          requisitionFormId: id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    });

    return true;
  }

  // 获取业务成本科目列表
  async getBusinessCostSubjectList(
    reqUser: IReqUser,
    requisitionBillId: string
  ) {
    const { tenantId, orgId } = reqUser;
    const result = await this.repository.selectBusinessCostSubjectList(
      tenantId,
      orgId,
      requisitionBillId
    );

    return result;
  }

  // 获取excel导出的页签数据
  async getA4ExportSheetData(
    reqUser: IReqUser,
    requisitionFormId: string,
    isA4Page: boolean
  ) {
    const result = await this.prisma.materialRequisitionFormDetail.findMany({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        requisitionFormId,
        parentId: null
      },
      select: {
        id: true,
        materialName: true,
        materialSpec: true,
        unit: true,
        actualQuantity: true,
        price: true,
        amount: true,
        businessCostSubjectDetailId: true
      }
    });

    const businessCostSubjectDetails =
      await this.repository.selectBusinessCostSubjectList(
        reqUser.tenantId,
        reqUser.orgId,
        requisitionFormId
      );

    const totalRow = { materialName: '合计', amount: 0 };
    for (const item of result) {
      const subjectDetail = businessCostSubjectDetails.find(
        (x) => x.id === item.businessCostSubjectDetailId
      );
      (item as any).businessCostSubjectDetailName = subjectDetail?.name || '';
      (item as any).planQuantity = item.actualQuantity;

      totalRow.amount = +add(totalRow.amount, item.amount || 0).toFixed(6);
    }

    const emptyRowCount = Utils.calculateEmptyRowCount(
      isA4Page ? 14 : 4,
      result.length + 1
    );
    for (let i = 0; i < emptyRowCount; i++) {
      result.push({} as any);
    }
    result.push(totalRow as any);

    return { records: result };
  }
}
