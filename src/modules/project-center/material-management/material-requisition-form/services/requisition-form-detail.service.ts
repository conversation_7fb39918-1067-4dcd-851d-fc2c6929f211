import { add, multiply, subtract } from '@ewing/infra-cloud-sdk';
import { BadRequestException, Injectable } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { v7 as uuidv7 } from 'uuid';

import { CommonRepositories } from '@/common/common-repositories';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { PrismaClient } from '@/prisma/generated';

import {
  CreateRequisitionDetailDto,
  MaterialCategoryListResponseDto,
  MaterialDetailListResponseDto,
  UpdateRequisitionDetailDto
} from '../material-requisition-form.dto';
import {
  SelectMaterialReceiveInfo,
  UpdateReceiveDetail
} from '../material-requisition-form.interface';
import { RequisitionFormRepository } from '../repositories/requisition-form.repository';
import { RequisitionFormDetailRepository } from '../repositories/requisition-form-detail.repository';

@Injectable()
export class MaterialRequisitionFormDetailService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: RequisitionFormDetailRepository,
    private readonly billRepository: RequisitionFormRepository
  ) {}

  // 获取领料单明细
  async getRequisitionDetailList(reqUser: IReqUser, requisitionBillId: string) {
    const result = await this.prisma.materialRequisitionFormDetail.findMany({
      where: {
        requisitionFormId: requisitionBillId,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      orderBy: {
        orderNo: 'asc'
      }
    });

    return result;
  }

  // 获取可选择的材料字典分类
  async getMaterialCategoryList(
    reqUser: IReqUser,
    requisitionBillId: string
  ): Promise<MaterialCategoryListResponseDto[]> {
    const requisitionBill = await this.prisma.materialRequisitionForm.findFirst(
      {
        where: {
          id: requisitionBillId,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: {
          year: true,
          month: true,
          day: true
        }
      }
    );
    if (!requisitionBill) return [];

    const { year, month, day } = requisitionBill;

    const result = await this.repository.selectMaterialCategoryList(
      reqUser,
      year,
      month,
      day
    );

    return result;
  }

  // 获取可选择的材料明细
  async getMaterialDetailList(
    reqUser: IReqUser,
    requisitionBillId: string,
    categoryId: string
  ): Promise<MaterialDetailListResponseDto[]> {
    const requisitionBill = await this.prisma.materialRequisitionForm.findFirst(
      {
        where: {
          id: requisitionBillId,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: {
          year: true,
          month: true,
          day: true
        }
      }
    );
    if (!requisitionBill) return [];

    const { year, month, day } = requisitionBill;
    const result = await this.repository.selectMaterialDetailList(
      reqUser,
      categoryId,
      year,
      month,
      day
    );

    // 查询业务成本科目
    const materialIds = result.map((item) => item.id) || [];
    const businessCostSubjectDetails =
      await this.billRepository.selectBusinessCostSubjectList(
        reqUser.tenantId,
        reqUser.orgId,
        requisitionBillId,
        materialIds
      );
    for (const item of result) {
      const tempSubjectDetails = businessCostSubjectDetails.filter(
        (subject) => subject.materialId === item.id
      );
      if (tempSubjectDetails && tempSubjectDetails.length === 1) {
        item.businessCostSubjectDetailId =
          tempSubjectDetails[0].businessCostSubjectDetailId;
      }
    }

    return result;
  }

  // 新增领料单明细
  async addRequisitionDetails(
    reqUser: IReqUser,
    requisitionBillId: string,
    data: CreateRequisitionDetailDto[]
  ) {
    const { tenantId, orgId } = reqUser;

    // 查询收料单据
    const requisitionBillInfo =
      await this.prisma.materialRequisitionForm.findFirst({
        where: {
          id: requisitionBillId,
          isDeleted: false,
          tenantId,
          orgId
        },
        select: { year: true, month: true, day: true }
      });
    if (!requisitionBillInfo) throw new BadRequestException('领料单不存在!');

    // 1. 获取领料单和退库单信息
    const { materialReceivingMap } =
      await this.selectReceivingAndReturnInventoryInfo(
        reqUser,
        requisitionBillInfo
      );

    // 2. 生成领料单明细入库的数据
    const {
      createRequisitionDetail,
      updateReceiveDetail,
      updateReversalDetail
    } = this.processAddRequisitionDetailData(
      reqUser,
      requisitionBillId,
      data,
      materialReceivingMap
    );

    // 3. 数据入库
    await this.addRequisitionDetailSaveTable(
      reqUser,
      requisitionBillId,
      createRequisitionDetail,
      updateReceiveDetail,
      updateReversalDetail
    );

    return true;
  }

  // 新增领料单明细-获取领料单和退库单信息
  async selectReceivingAndReturnInventoryInfo(
    reqUser: IReqUser,
    requisitionBillInfo: any
  ): Promise<{
    materialReceivingMap: Record<string, SelectMaterialReceiveInfo[]>;
  }> {
    const { tenantId, orgId } = reqUser;
    const { year, month, day } = requisitionBillInfo;
    const receiveBill: SelectMaterialReceiveInfo[] =
      await this.repository.selectMaterialReceiveInfo(
        tenantId,
        orgId,
        year,
        month,
        day
      );

    const reversalMaterialInventoryMap: Record<string, any> =
      receiveBill.reduce((pre: any, cur: any) => {
        if (cur.itemType !== 'reversal') return pre;
        if (!pre[cur.materialId]) {
          pre[cur.materialId] = 0;
        }
        pre[cur.materialId] = add(
          pre[cur.materialId],
          cur.inventoryQuantity
        ).toFixed(8);
        return pre;
      }, {});

    for (const item of receiveBill) {
      if (
        item.itemType === 'reversal' ||
        !reversalMaterialInventoryMap[item.materialId]
      )
        continue;

      if (
        item.inventoryQuantity >=
          reversalMaterialInventoryMap[item.materialId] ||
        0
      ) {
        item.inventoryQuantity = +subtract(
          item.inventoryQuantity,
          reversalMaterialInventoryMap[item.materialId] || 0
        ).toFixed(8);
      } else {
        item.inventoryQuantity = 0;
        reversalMaterialInventoryMap[item.materialId] = subtract(
          reversalMaterialInventoryMap[item.materialId] || 0,
          item.inventoryQuantity
        ).toFixed(8);
      }
    }

    const materialReceivingMap = receiveBill.reduce(
      (acc, cur) => {
        if (!acc[cur.materialId]) {
          acc[cur.materialId] = [];
        }
        acc[cur.materialId].push(cur);
        return acc;
      },
      {} as Record<string, SelectMaterialReceiveInfo[]>
    );

    return { materialReceivingMap };
  }

  // 新增领料单明细-生成领料单明细入库的数据
  processAddRequisitionDetailData(
    reqUser: IReqUser,
    requisitionBillId: string,
    data: CreateRequisitionDetailDto[],
    materialReceivingMap: Record<string, SelectMaterialReceiveInfo[]>,
    generateParent: boolean = true,
    parentId?: string
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    // 处理领料单入库数据和收料单、退库单更新数据
    const createRequisitionDetail: any[] = [];
    const updateReceiveDetail: UpdateReceiveDetail[] = [];
    const updateReversalDetail: UpdateReceiveDetail[] = [];
    for (const item of data) {
      const id = generateParent ? uuidv7() : parentId;

      let tempActualQuantity: any = item.actualQuantity || 0; // 领料数量
      // 从领料单里找
      const receivingBillList = materialReceivingMap[item.materialId] || [];
      for (const receivingBill of receivingBillList) {
        const { itemType, inventoryQuantity } = receivingBill;
        if (itemType === 'receiving' && inventoryQuantity <= 0) {
          // 如果是收料单，并且库存为0，说明库存全部来自于退库
          updateReceiveDetail.push({
            id: receivingBill.detailId,
            inventoryQuantity: 0
          });
          continue;
        }

        if (tempActualQuantity <= 0) break;

        let actualQuantity;
        if (receivingBill.inventoryQuantity >= tempActualQuantity) {
          const updateData = {
            id: receivingBill.detailId,
            inventoryQuantity: +subtract(
              receivingBill.inventoryQuantity,
              tempActualQuantity
            ).toFixed(8)
          };
          if (itemType === 'receiving') {
            updateReceiveDetail.push(updateData);
          } else if (itemType === 'reversal') {
            updateReversalDetail.push(updateData);
          }

          actualQuantity = tempActualQuantity;
          tempActualQuantity = 0;
        } else {
          const updateData = {
            id: receivingBill.detailId,
            inventoryQuantity: 0
          };
          if (itemType === 'receiving') {
            updateReceiveDetail.push(updateData);
          } else if (itemType === 'reversal') {
            updateReversalDetail.push(updateData);
          }
          actualQuantity = receivingBill.inventoryQuantity;
          tempActualQuantity = subtract(
            tempActualQuantity,
            receivingBill.inventoryQuantity
          ).toFixed(8);
        }

        createRequisitionDetail.push({
          parentId: id,
          requisitionFormId: requisitionBillId,
          materialId: item.materialId,
          materialName: receivingBill.code,
          materialSpec: receivingBill.billDate,
          materialReceivingId: receivingBill.id,
          materialReceivingInventoryId: receivingBill.detailId,
          unit: item.unit,
          inventoryQuantity: item.inventoryQuantity,
          actualQuantity,
          actualInventoryQuantity: actualQuantity,
          price: receivingBill.price,
          amount: multiply(actualQuantity, receivingBill.price).toFixed(8),
          tenantId,
          orgId,
          createBy: userId,
          updateBy: userId
        });
      }

      // 添加领料单明细
      if (generateParent) {
        createRequisitionDetail.push({
          ...item,
          actualInventoryQuantity: item.actualQuantity,
          requisitionFormId: requisitionBillId,
          id,
          tenantId,
          orgId,
          createBy: userId,
          updateBy: userId
        });
      }
    }

    return {
      createRequisitionDetail,
      updateReceiveDetail,
      updateReversalDetail
    };
  }

  // 新增领料单明细，数据入库
  async addRequisitionDetailSaveTable(
    reqUser: IReqUser,
    requisitionBillId: string,
    createRequisitionDetail: any[],
    updateReceiveDetail: UpdateReceiveDetail[],
    updateReversalDetail: UpdateReceiveDetail[]
  ) {
    await this.prisma.$transaction(async (tx) => {
      // 创建领料单明细
      await tx.materialRequisitionFormDetail.createMany({
        data: createRequisitionDetail
      });

      if (!isEmpty(updateReceiveDetail)) {
        // 更新收料单
        await this.repository.bulkUpdateMaterialReceiveDetail(
          reqUser,
          tx as PrismaService,
          updateReceiveDetail
        );
      }

      if (!isEmpty(updateReversalDetail)) {
        // 更新退库单
        await this.repository.bulkUpdateMaterialReturnInventoryDetail(
          reqUser,
          tx as PrismaService,
          updateReversalDetail
        );
      }

      // 更新父级的单价和金额
      await this.repository.updateParentDetailPriceAndAmount(
        reqUser,
        tx as PrismaService,
        requisitionBillId
      );
    });
  }

  // 删除领料单明细
  async deleteRequisitionDetail(reqUser: IReqUser, id: string) {
    const { tenantId, orgId, id: userId } = reqUser;

    const detail = await this.prisma.materialRequisitionFormDetail.findFirst({
      where: {
        id,
        isDeleted: false,
        tenantId: tenantId,
        orgId: orgId
      },
      select: {
        requisitionFormId: true
      }
    });
    if (!detail) throw new BadRequestException('领料单明细不存在！');

    await this.prisma.$transaction(async (tx) => {
      // 删除领料单明细，需要返还收料单和退库单的库存
      await this.repository.updateMaterialReceivingDetailInventory(
        tx as PrismaClient,
        reqUser,
        detail.requisitionFormId,
        id
      );
      await this.repository.updateMaterialReversalDetailInventory(
        tx as PrismaClient,
        reqUser,
        detail.requisitionFormId,
        id
      );

      // 删除父级（材料）
      await tx.materialRequisitionFormDetail.update({
        data: {
          isDeleted: true,
          updateBy: userId
        },
        where: {
          id,
          isDeleted: false,
          tenantId: tenantId,
          orgId: orgId
        }
      });
      // 删除子明细
      await tx.materialRequisitionFormDetail.updateMany({
        data: {
          isDeleted: true,
          updateBy: userId
        },
        where: {
          parentId: id,
          isDeleted: false,
          tenantId: tenantId,
          orgId: orgId
        }
      });
    });

    return true;
  }

  // 编辑领料单明细
  async editRequisitionDetail(
    reqUser: IReqUser,
    data: UpdateRequisitionDetailDto
  ) {
    const oldDetailInfo =
      await this.prisma.materialRequisitionFormDetail.findFirst({
        where: {
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          id: data.id
        },
        select: {
          requisitionFormId: true,
          actualQuantity: true,
          businessCostSubjectDetailId: true,
          materialId: true,
          materialName: true,
          materialSpec: true,
          unit: true,
          price: true
        }
      });

    if (!oldDetailInfo) throw new BadRequestException('领料单明细数据不存在');

    // 是否改变了实收数量
    const isChangeActualQuantity =
      +(data.actualQuantity || 0) !== +(oldDetailInfo.actualQuantity || 0);

    if (isChangeActualQuantity) {
      // 校验库存是否足够
      const inventoryInfo =
        await this.repository.selectMaterialInventoryInfoInfo(
          reqUser.tenantId,
          reqUser.orgId,
          oldDetailInfo.requisitionFormId,
          oldDetailInfo.materialId
        );
      if (
        data.actualQuantity &&
        data.actualQuantity >
          (inventoryInfo?.inventoryQuantity || 0) +
            +(oldDetailInfo.actualQuantity || 0)
      ) {
        throw new BadRequestException('库存不足');
      }
    }

    // 先删除数据，在重新分配库存，用两个事务
    await this.prisma.$transaction(async (tx) => {
      await tx.materialRequisitionFormDetail.update({
        where: {
          id: data.id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        data: { ...data, updateBy: reqUser.id }
      });

      if (isChangeActualQuantity) {
        // 删除领料单明细，需要返还收料单和退库单的库存
        await this.repository.updateMaterialReceivingDetailInventory(
          tx as PrismaClient,
          reqUser,
          oldDetailInfo.requisitionFormId,
          data.id
        );
        await this.repository.updateMaterialReversalDetailInventory(
          tx as PrismaClient,
          reqUser,
          oldDetailInfo.requisitionFormId,
          data.id
        );
        // 删除旧的子级明细
        await tx.materialRequisitionFormDetail.updateMany({
          where: {
            parentId: data.id,
            isDeleted: false,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId
          },
          data: { isDeleted: true, updateBy: reqUser.id }
        });
      }
    });

    if (!isChangeActualQuantity) return true;
    // 修改实收数量
    await this.updateActualQuantity(reqUser, oldDetailInfo, data);

    return true;
  }

  // 编辑领料单明细，更新实收数量
  async updateActualQuantity(
    reqUser: IReqUser,
    oldDetailInfo: Record<string, any>,
    data: UpdateRequisitionDetailDto
  ) {
    // 新增子级明细，按新的实收数量重新分配
    const requisitionBillInfo =
      await this.prisma.materialRequisitionForm.findFirst({
        where: {
          id: oldDetailInfo.requisitionFormId,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: { year: true, month: true, day: true }
      });

    // 1. 获取领料单和退库单信息
    const { materialReceivingMap } =
      await this.selectReceivingAndReturnInventoryInfo(
        reqUser,
        requisitionBillInfo
      );

    // 2. 生成领料单明细入库的数据
    const detailData: CreateRequisitionDetailDto[] = [
      {
        materialId: oldDetailInfo.materialId,
        materialName: oldDetailInfo.materialName || '',
        materialSpec: oldDetailInfo.materialSpec || '',
        unit: oldDetailInfo.unit,
        inventoryQuantity: +(oldDetailInfo.inventoryQuantity || 0),
        actualQuantity: data.actualQuantity
      }
    ];
    const {
      createRequisitionDetail,
      updateReceiveDetail,
      updateReversalDetail
    } = this.processAddRequisitionDetailData(
      reqUser,
      oldDetailInfo.requisitionFormId,
      detailData,
      materialReceivingMap,
      false,
      data.id
    );

    // 3.数据入库
    await this.prisma.$transaction(async (tx) => {
      // 新增子级明细
      await tx.materialRequisitionFormDetail.createMany({
        data: createRequisitionDetail
      });

      // 处理收料单和退库单库存
      if (!isEmpty(updateReceiveDetail)) {
        await this.repository.bulkUpdateMaterialReceiveDetail(
          reqUser,
          tx as PrismaClient,
          updateReceiveDetail
        );
      }
      if (!isEmpty(updateReversalDetail)) {
        await this.repository.bulkUpdateMaterialReturnInventoryDetail(
          reqUser,
          tx as PrismaClient,
          updateReversalDetail
        );
      }

      // 更新父级的单价和金额
      await this.repository.updateParentDetailPriceAndAmount(
        reqUser,
        tx as PrismaService,
        oldDetailInfo.requisitionFormId
      );
    });
  }

  // 领料单明细上移下移
  async moveRequisitionDetail(reqUser: IReqUser, fromId: string, toId: string) {
    await CommonRepositories.changeDataOrderNo(this.prisma, {
      tenantId: reqUser.tenantId,
      orgId: reqUser.orgId,
      fromId,
      toId,
      tableName: 'material_requisition_form_detail'
    });
    return true;
  }
}
