import { FileClientService } from '@ewing/infra-cloud-sdk';
import {
  BadRequestException,
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable
} from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as dayjs from 'dayjs';
import * as ExcelJS from 'exceljs';
import * as uuid from 'uuid';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { Utils } from '@/common/utils';
import { PlatformService } from '@/modules/platform/platform.service';
import {
  AuditStatus,
  BusinessBaseInfoVersionStatus,
  ContractTemplateClassifyType,
  FieldType,
  FulfillmentStatus,
  MaterialContract,
  PartyBType,
  Prisma,
  ProposedStatus,
  SubmitStatus,
  TaxpayerQualification,
  VersionStatus
} from '@/prisma/generated';

import { ContractConcreteSurchargeService } from '../contract-concrete-surcharge/contract-concrete-surcharge.service';
import { ContractMaterialDetailService } from '../contract-material-detail/contract-material-detail.service';
import { UnitCalculationService } from '../unit-calculation/unit-calculation.service';
import {
  MaterialContractCreateDto,
  MaterialContractUpdateAuditStatusDto,
  MaterialContractUpdateDto,
  MaterialContractUpdateSubmitStatusDto,
  QueryMaterialContractDto
} from './material-contract.dto';
import { PartyBCompanyResDto, SaveRuleFieldDto } from './other.dto';

@Injectable()
export class MaterialContractService {
  // 可存文本的类型
  private readonly textValueType = [
    'TEXT' as FieldType,
    'DATE' as FieldType,
    'ENUM' as FieldType
  ];
  private readonly decimalValueType = [
    'PERCENT' as FieldType,
    'NUMBER' as FieldType
  ];
  constructor(
    private readonly prisma: PrismaService,
    private readonly platformService: PlatformService,
    @Inject(forwardRef(() => ContractMaterialDetailService)) //
    private readonly contractMaterialDetailService: ContractMaterialDetailService,
    private readonly fileClientService: FileClientService,
    private readonly contractConcreteSurchargeService: ContractConcreteSurchargeService,
    private readonly unitCalculationService: UnitCalculationService
  ) {}

  async getPartyACompanyList(
    req: Request,
    reqUser: IReqUser,
    isFilterStatus: boolean
  ) {
    const { tenantId, orgId } = reqUser;
    // 查询当前公司的所有上级公司
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      tenantId,
      orgId
    );
    const company = await this.prisma.businessBaseInfo.findMany({
      select: {
        id: true,
        companyVersion: true
      },
      where: {
        isDeleted: false,
        status: isFilterStatus
          ? BusinessBaseInfoVersionStatus.PUBLISHED
          : undefined,
        tenantId,
        orgId: {
          in: topOrg
        }
      },
      orderBy: [
        {
          sort: 'asc'
        },
        {
          companyVersion: 'asc'
        }
      ]
    });
    return company.map((item) => {
      return {
        id: item.id,
        name: item.companyVersion
      };
    });
  }

  async getPartyACompanyForSelect(
    req: Request,
    reqUser: IReqUser,
    isFilterStatus: boolean
  ) {
    const { tenantId, orgId } = reqUser;
    // 查询当前公司的所有上级公司
    const topOrg = await this.platformService.getOrgParentIds(
      req,
      tenantId,
      orgId
    );
    const company = await this.prisma.businessBaseInfo.findMany({
      select: {
        id: true,
        companyName: true
      },
      where: {
        isDeleted: false,
        status: isFilterStatus
          ? BusinessBaseInfoVersionStatus.PUBLISHED
          : undefined,
        tenantId,
        orgId: {
          in: topOrg
        }
      },
      orderBy: [
        {
          sort: 'asc'
        },
        {
          companyName: 'asc'
        }
      ]
    });
    return company.map((item) => {
      return {
        id: item.id,
        name: item.companyName
      };
    });
  }

  async getPartBCompanyList(
    req: Request,
    reqUser: IReqUser,
    isFilterStatus: boolean
  ) {
    const { tenantId, orgId } = reqUser;
    const res: PartyBCompanyResDto[] = [];
    // 查询当前公司的所有上级公司
    const orgs = await this.platformService.getOrgParentIds(
      req,
      tenantId,
      orgId
    );
    // 查询公司
    const company = await this.prisma.businessBaseInfo.findMany({
      select: {
        id: true,
        companyVersion: true
      },
      where: {
        isDeleted: false,
        status: isFilterStatus
          ? BusinessBaseInfoVersionStatus.PUBLISHED
          : undefined,
        tenantId,
        orgId: {
          in: orgs
        }
      },
      orderBy: [
        {
          sort: 'asc'
        },
        {
          companyVersion: 'asc'
        }
      ]
    });
    // 查询当前组织的最顶级父级
    const topOrg = await this.platformService.getTopOrg(req, tenantId, orgId);
    // 查询供应商
    const supplier = await this.prisma.supplierDirectory.findMany({
      select: {
        id: true,
        simpleName: true
      },
      where: {
        isDeleted: false,
        isPublished: isFilterStatus ? true : undefined,
        tenantId,
        orgId: topOrg
      },
      orderBy: {
        publishAt: 'desc'
      }
    });
    supplier.forEach((item) => {
      res.push({
        id: item.id,
        name: item.simpleName,
        type: 'SUPPLIER'
      });
    });
    // 数据合并
    company.forEach((item) => {
      res.push({
        id: item.id,
        name: item.companyVersion || '',
        type: 'COMPANY'
      });
    });
    return res;
  }

  async getPartBCompanyForSelect(
    req: Request,
    reqUser: IReqUser,
    isFilterStatus: boolean
  ) {
    const { tenantId, orgId } = reqUser;
    const res: PartyBCompanyResDto[] = [];
    // 查询当前公司的所有上级公司
    const orgs = await this.platformService.getOrgParentIds(
      req,
      tenantId,
      orgId
    );
    // 查询公司
    const company = await this.prisma.businessBaseInfo.findMany({
      select: {
        id: true,
        companyName: true
      },
      where: {
        isDeleted: false,
        status: isFilterStatus
          ? BusinessBaseInfoVersionStatus.PUBLISHED
          : undefined,
        tenantId,
        orgId: {
          in: orgs
        }
      },
      orderBy: [
        {
          sort: 'asc'
        },
        {
          companyName: 'asc'
        }
      ]
    });
    // 查询当前组织的最顶级父级
    const topOrg = await this.platformService.getTopOrg(req, tenantId, orgId);
    // 查询供应商
    const supplier = await this.prisma.supplierDirectory.findMany({
      select: {
        id: true,
        fullName: true
      },
      where: {
        isDeleted: false,
        isPublished: isFilterStatus ? true : undefined,
        tenantId,
        orgId: topOrg
      },
      orderBy: {
        publishAt: 'desc'
      }
    });
    supplier.forEach((item) => {
      res.push({
        id: item.id,
        name: item.fullName,
        type: 'SUPPLIER'
      });
    });
    // 数据合并
    company.forEach((item) => {
      res.push({
        id: item.id,
        name: item.companyName || '',
        type: 'COMPANY'
      });
    });
    return res;
  }

  async getContractTemplateList(req: Request, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    // 查询当前组织的最顶级父级
    const topOrg = await this.platformService.getTopOrg(req, tenantId, orgId);
    // 获取材料-物资采购,材料-商品混凝土，材料-租赁周转材料
    const contractTemplateClassifyTypeList = [
      'MATERIALS_PURCHASING' as ContractTemplateClassifyType,
      'MATERIALS_COMMERCIAL_CONCRETE' as ContractTemplateClassifyType,
      'MATERIALS_LEASING_TURNOVER' as ContractTemplateClassifyType
    ];
    return await this.prisma.contractTemplate.findMany({
      where: {
        classify: {
          in: contractTemplateClassifyTypeList
        },
        tenantId,
        orgId: topOrg,
        isDeleted: false,
        versionStatus: VersionStatus.PUBLISHED
      },
      select: {
        id: true,
        name: true,
        classify: true
      },
      orderBy: [
        {
          sort: 'asc'
        },
        {
          createAt: 'desc'
        }
      ]
    });
  }

  async getList(
    req: Request,
    reqUser: IReqUser,
    query: QueryMaterialContractDto
  ) {
    // 查询合同
    const materialContract = await this.prisma.$queryRaw<any[]>`
      WITH RECURSIVE contract_tree AS (
          -- 递归CTE：获取所有合同及其父子关系
          SELECT 
              id,
              parent_id,
              id AS root_id  -- 记录根节点ID（顶级父合同ID）
          FROM 
              material_contract
          WHERE 
              tenant_id = ${reqUser.tenantId}
              AND org_id = ${reqUser.orgId}
              AND is_deleted = false
              AND parent_id IS NULL  -- 从顶级父合同开始
          
          UNION ALL
          
          SELECT 
              mc.id,
              mc.parent_id,
              ct.root_id  -- 子合同继承继承根节点ID
          FROM 
              material_contract mc
          INNER JOIN 
              contract_tree ct ON mc.parent_id = ct.id
          WHERE 
              mc.tenant_id = ${reqUser.tenantId}
              AND mc.org_id = ${reqUser.orgId}
              AND mc.is_deleted = false
      ),
      root_contracts_with_inspection AS (
          -- 检查根合同是否有对应的应的材料进场验收单
          SELECT DISTINCT
              ct.root_id,
              TRUE AS has_inspection  -- 存在关联的验收单则为TRUE
          FROM 
              contract_tree ct
          INNER JOIN 
              material_incoming_inspection mii 
              ON mii.contract_id = ct.root_id  -- 关联父级合同ID
              AND mii.tenant_id = ${reqUser.tenantId}
              AND mii.org_id = ${reqUser.orgId}
              AND mii.is_deleted = false
              AND mii.submit_status::TEXT = ${SubmitStatus.SUBMITTED}
      ),
      contract_with_status AS (
          SELECT 
              mc.id,
              mc.parent_id,
              mc.org_id,
              mc.tenant_id,
              mc.contract_template_id,
              mc.name,
              mc.code,
              mc.party_a,
              mc.party_b,
              mc.party_b_type,
              u.nickname as creator,
              mc.create_at,
              mc.submit_status,
              mc.audit_status,
              CASE 
                  WHEN rcwi.has_inspection THEN 'IN_PROGRESS'
                  ELSE 'NOT_STARTED'
              END AS fulfillment_status,
              mc.proposed_status,
              mc.remark,
              mc.file_ext,
              mc.file_key,
              mc.file_name,
              mc.file_size,
              mc.file_content_type,
              mc.price_type,
              mc.amount,
              mc.update_at,
              mc.create_by,
              ct.name as contract_template_name,
              ct.classify as contract_template_type
          FROM 
              material_contract mc
          INNER JOIN 
              contract_template ct ON ct.id = mc.contract_template_id AND ct.tenant_id = mc.tenant_id
          INNER JOIN 
              platform_meta.user u ON u.id = mc.create_by
          INNER JOIN 
              contract_tree ct_tree ON mc.id = ct_tree.id
          LEFT JOIN 
              root_contracts_with_inspection rcwi ON ct_tree.root_id = rcwi.root_id
          WHERE 
              mc.tenant_id = ${reqUser.tenantId}
              AND mc.org_id = ${reqUser.orgId}
              ${query.submitStatus ? Prisma.sql`and mc.submit_status::TEXT = ${query.submitStatus}` : Prisma.empty}
              ${query.auditStatus ? Prisma.sql`and mc.audit_status::TEXT = ${query.auditStatus}` : Prisma.empty}
              ${query.isQueryChild ? Prisma.sql`and mc.parent_id is null` : Prisma.empty}
              AND mc.is_deleted = false
      )
      SELECT *
      FROM contract_with_status
      ORDER BY create_at desc;
    `;
    // 查询甲方
    const partyA = await this.getPartyACompanyList(req, reqUser, false);
    // 查询乙方
    const partyB = await this.getPartBCompanyList(req, reqUser, false);
    // 数据合并
    const res = materialContract.map((item) => {
      return {
        ...item,
        partyAName: partyA.find((item1) => item1.id === item.partyA)?.name,
        partyBName: partyB.find((item2) => item2.id === item.partyB)?.name
      };
    });
    // 父级倒序
    const parents = res.filter((item) => item.parentId === null);
    // 子级正序
    const children = res.filter((item) => item.parentId !== null);

    children.sort(
      (a: any, b: any) =>
        new Date(a.createAt).getTime() - new Date(b.createAt).getTime()
    );

    // 4. 合并父级和排序后的子级
    return [...parents, ...children];
  }

  async add(req: Request, reqUser: IReqUser, data: MaterialContractCreateDto) {
    const { orgId, tenantId, nickname, id: userId } = reqUser;
    let newData: MaterialContract;
    // 唯一性校验
    await this.checkUnique(
      tenantId,
      orgId,
      data.name,
      data.code,
      null,
      data.parentId
    );
    // 查询模版的信息
    const template = await this.prisma.contractTemplate.findUnique({
      where: {
        id: data.contractTemplateId
      },
      select: {
        fileContentType: true,
        fileName: true,
        fileKey: true,
        fileSize: true,
        fileExt: true,
        classify: true
      }
    });
    await this.prisma.$transaction(async (txPrisma) => {
      if (data.parentId) {
        // 查询父级
        const parent = await this.getOne(data.parentId, reqUser);
        // 拿父级的甲方id和乙方id查询最新的名称
        const nameObj = await this.getName(
          parent.partyA,
          parent.partyB,
          parent.partyBType,
          reqUser
        );
        newData = await txPrisma.materialContract.create({
          data: {
            ...data,
            partyAName: nameObj.partyAEndName as string,
            partyBName: nameObj.partyBEndName as string,
            partyAEndName: nameObj.partyAEndName as string,
            partyBEndName: nameObj.partyBEndName as string,
            fulfillmentStatus: parent.fulfillmentStatus,
            amount: parent.amount,
            priceType: parent.priceType,
            orgId: orgId,
            tenantId: tenantId,
            creator: nickname,
            createBy: userId,
            updateBy: userId
          }
        });
        // 校验父级
        await this.checkParent(
          txPrisma as PrismaService,
          reqUser,
          data.contractTemplateId,
          parent,
          newData.id
        );
        // 修改子级
        await txPrisma.materialContract.update({
          where: {
            id: newData.id,
            isDeleted: false
          },
          data: {
            fullId: parent?.id ? parent?.id + '|' + newData.id : newData.id,
            fullName: parent?.name
              ? parent?.name + '|' + newData.name
              : newData.name,
            updateBy: userId
          }
        });
        // 初始化合同材料明细的单位换算
        await this.unitCalculationService.init(
          txPrisma as PrismaService,
          newData.id,
          reqUser,
          data.parentId
        );
      } else {
        newData = await txPrisma.materialContract.create({
          data: {
            ...data,
            partyAEndName: data.partyAName,
            partyBEndName: data.partyBName,
            orgId: orgId,
            tenantId: tenantId,
            creator: nickname,
            createBy: userId,
            updateBy: userId
          }
        });
        // 初始化合同附加费
        if (
          template?.classify ===
          ContractTemplateClassifyType.MATERIALS_COMMERCIAL_CONCRETE
        ) {
          await this.contractConcreteSurchargeService.init(
            txPrisma as PrismaService,
            reqUser,
            newData.id
          );
        }
      }
      // 初始化合同字段
      await this.initContractTemplateField(
        req,
        txPrisma as PrismaService,
        newData.id,
        data.contractTemplateId,
        reqUser,
        data,
        data.parentId
      );
      // copy文件到obs
      await this.copyTemplateFile(
        txPrisma as PrismaService,
        reqUser,
        newData?.id,
        data.parentId,
        template
      );
    });
    return true;
  }

  async getName(
    partyA: string,
    partyB: string,
    partyBType: PartyBType,
    reqUser: IReqUser
  ) {
    const { tenantId, orgId } = reqUser;
    const partyAObj = await this.prisma.businessBaseInfo.findUnique({
      select: {
        id: true,
        companyName: true
      },
      where: {
        id: partyA,
        isDeleted: false,
        tenantId
      }
    });
    let partyBObj: any;
    if (partyBType === PartyBType.COMPANY) {
      partyBObj = await this.prisma.businessBaseInfo.findUnique({
        select: {
          id: true,
          companyName: true
        },
        where: {
          id: partyB,
          isDeleted: false,
          tenantId
        }
      });
    } else {
      partyBObj = await this.prisma.supplierDirectory.findUnique({
        select: {
          id: true,
          simpleName: true
        },
        where: {
          id: partyB,
          isDeleted: false,
          tenantId
        }
      });
    }
    return {
      partyAEndName: partyAObj?.companyName,
      partyBEndName:
        partyBType === PartyBType.COMPANY
          ? partyBObj?.companyName
          : partyBObj?.simpleName
    };
  }

  async copyTemplateFile(
    txPrisma: PrismaService,
    reqUser: IReqUser,
    newContractId: string,
    parentId?: string | null,
    template?: any
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    if (template) {
      const fileKey = parentId
        ? uuid.v7() + '.docx'
        : uuid.v7() + '.' + template.fileKey.split('.')[1];
      if (!parentId) {
        await this.fileClientService.copyFile({
          tenantId,
          product: 'ecost',
          copySource: {
            key: parentId ? 'contract-template.docx' : template.fileKey
          },
          key: fileKey
        });
      } else {
        await this.fileClientService.upload({
          data: '',
          tenantId,
          product: 'ecost',
          key: fileKey
        });
      }

      await txPrisma.materialContract.update({
        where: {
          id: newContractId,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          fileExt: parentId ? 'docx' : template.fileKey.split('.')[1],
          fileContentType: template.fileContentType,
          fileKey: fileKey,
          fileName: parentId ? '空白模版.docx' : template.fileName,
          fileSize: parentId ? '0' : template.fileSize,
          updateBy: userId
        }
      });
    }
  }

  async checkParent(
    txPrisma: PrismaService,
    reqUser: IReqUser,
    contractTemplateId: string,
    parent: any,
    newContractId: string
  ) {
    // 查询父级下是否还有补充协议
    const lastChild = await this.prisma.materialContract.findFirst({
      where: {
        parentId: parent.id,
        isDeleted: false
      },
      orderBy: {
        createAt: 'desc'
      }
    });
    if (lastChild) {
      // 校验补充协议
      this.checkChildStatus(lastChild);
    } else {
      // 不存在补充协议
      // 校验父级
      this.checkParentStatus(parent);
    }
    // 取父级合同的数据放到补充协议里面
    await this.contractMaterialDetailService.addSupplementaryAgreement(
      txPrisma,
      reqUser,
      contractTemplateId,
      parent.id,
      newContractId
    );
  }

  checkChildStatus(parent: any) {
    if (parent.auditStatus !== 'APPROVED') {
      throw new BadRequestException('上一补充协议未完成审批');
    }
  }

  checkParentStatus(parent: any) {
    if (parent.fulfillmentStatus === 'COMPLETED') {
      throw new BadRequestException('履约已完成不允许新增补充协议');
    }
    if (parent.auditStatus !== 'APPROVED') {
      throw new BadRequestException('未完成审批不允许新增补充协议');
    }
  }

  async initContractTemplateField(
    req: Request,
    txPrisma: PrismaService,
    materialContractId: string,
    contractTemplateId: string,
    reqUser: IReqUser,
    data: MaterialContractCreateDto,
    parentId?: string | null
  ) {
    if (parentId) {
      // 取父级的字段塞进子级
      await this.initChild(
        req,
        txPrisma,
        materialContractId,
        contractTemplateId,
        reqUser,
        data,
        parentId
      );
    } else {
      // 初始化父级
      await this.initParent(
        req,
        txPrisma,
        materialContractId,
        contractTemplateId,
        reqUser,
        data
      );
    }
  }

  // 初始化子级
  async initChild(
    req: Request,
    txPrisma: PrismaService,
    materialContractId: string,
    contractTemplateId: string,
    reqUser: IReqUser,
    data: MaterialContractCreateDto,
    parentId: string
  ) {
    const { orgId, tenantId } = reqUser;
    const { code } = data;

    // 查询父级下是否还有补充协议
    const lastChild = await this.prisma.materialContract.findFirst({
      where: {
        parentId: parentId,
        isDeleted: false
      },
      orderBy: {
        createAt: 'desc'
      }
    });
    const contractId = lastChild ? lastChild.id : parentId;
    // 查询父级的字段
    const parentFields = await this.prisma.$queryRaw<any[]>`
      SELECT mcfr.text_value, mcfr.decimal_value, ctfr.id as contract_template_field_rule_id, esct.code, esct.field_type
      from material_contract_field_rule mcfr
      JOIN contract_template_field_rule ctfr
      on ctfr.contract_template_id = mcfr.contract_template_id
        and ctfr.id = mcfr.contract_template_field_rule_id
        and ctfr.is_deleted = false
      JOIN field_rule esct ON esct.is_deleted = false AND esct.id = ctfr.field_rule_id
        WHERE mcfr.material_contract_id = ${contractId}
        and mcfr.org_id = ${orgId} 
        and mcfr.tenant_id = ${tenantId} 
        and mcfr.is_deleted = false
    `;
    await txPrisma.materialContractFieldRule.createMany({
      data: parentFields.map((item) => {
        const reset = {
          textValue: this.textValueType.includes(item.fieldType)
            ? item.textValue
            : null,
          decimalValue:
            this.decimalValueType.includes(item.fieldType) ||
            item.code === '增值税税率'
              ? item.decimalValue
              : null,
          orgId: reqUser.orgId,
          tenantId: reqUser.tenantId,
          materialContractId,
          contractTemplateId,
          contractTemplateFieldRuleId: item.contractTemplateFieldRuleId,
          updateBy: reqUser.id,
          createBy: reqUser.id
        };
        if (item.code === '签约日期') {
          reset.textValue = null;
        }
        if (item.code === '合同编号') {
          reset.textValue = code;
        }
        return {
          ...reset
        };
      })
    });
  }

  // 初始化父级
  async initParent(
    req: Request,
    txPrisma: PrismaService,
    materialContractId: string,
    contractTemplateId: string,
    reqUser: IReqUser,
    data: MaterialContractCreateDto
  ) {
    const { orgId, tenantId } = reqUser;
    const { partyA, partyB, partyBType, code } = data;

    // 1. 使用Promise.all并行执行所有独立查询
    const [
      ruleList,
      orgInfo,
      projectInfo,
      partyAInfo,
      partyBInfo,
      taxRateInfo
    ] = await Promise.all([
      // 查询字段规则
      txPrisma.$queryRaw<any[]>`
        SELECT ctfr.id as contract_template_field_rule_id, esct.code, esct.field_type
        FROM contract_template_field_rule ctfr
        JOIN field_rule esct ON esct.is_deleted = false AND esct.id = ctfr.field_rule_id
        WHERE ctfr.is_deleted = false AND ctfr.contract_template_id = ${contractTemplateId}
      `,
      // 获取组织信息
      this.platformService.getCurrentOrgInfo(req, tenantId, orgId),
      // 获取项目信息
      txPrisma.basicProjectInfoLedger.findMany({
        select: {
          id: true,
          value: true,
          basicProjectInfoFieldDetail: { select: { name: true } }
        },
        where: {
          orgId,
          tenantId,
          isDeleted: false,
          basicProjectInfoFieldDetail: {
            isDeleted: false,
            basicProjectInfoCategory: { isDeleted: false, isDefault: true }
          }
        }
      }),
      // 获取甲方信息
      txPrisma.businessBaseInfo.findFirst({
        where: {
          id: partyA,
          isDeleted: false,
          status: BusinessBaseInfoVersionStatus.PUBLISHED,
          tenantId
        }
      }),
      // 获取乙方信息（根据类型选择查询）
      partyBType === PartyBType.COMPANY
        ? txPrisma.businessBaseInfo.findFirst({
            where: {
              id: partyB,
              isDeleted: false,
              status: BusinessBaseInfoVersionStatus.PUBLISHED,
              tenantId
            },
            orderBy: [{ sort: 'asc' }, { companyName: 'asc' }]
          })
        : txPrisma.supplierDirectory.findFirst({
            where: {
              id: partyB,
              isDeleted: false,
              isPublished: true,
              tenantId
            }
          }),
      // 获取税率信息
      txPrisma.$queryRaw<any[]>`
        SELECT mde.tax_rate, mde.execute_date, mde.name
        FROM account_taxrate_dictionary_detail amde
        JOIN taxrate_dictionary_detail mde ON mde.id = amde.detail_id
          AND mde.is_deleted = false
          AND mde.is_active = true
        WHERE amde.is_deleted = false
          AND amde.tenant_id = ${tenantId}
          AND amde.org_id = ${orgId}
      `
    ]);

    // 2. 创建字段映射表提升查找性能
    const fieldMap = new Map<string, any>(
      ruleList.map((item) => [item.code, item])
    );

    // 3. 集中处理字段赋值逻辑
    // 组织信息
    this.assignOrgInfo(fieldMap, orgInfo);

    // 项目信息
    this.assignProjectInfo(fieldMap, projectInfo);

    // 合同方信息
    this.assignPartyInfo(fieldMap, partyAInfo, partyBType, partyBInfo);

    // 税率信息
    this.assignTaxRateInfo(fieldMap, taxRateInfo);

    // 4. 特殊字段处理（合同编号）
    if (fieldMap.has('合同编号')) {
      fieldMap.get('合同编号').value = code;
    }

    // 5. 转换回数组并处理空值
    const processedRules = Array.from(fieldMap.values()).map((item) => ({
      ...item,
      value: item.value ?? null // 使用空值合并运算符
    }));

    // 6. 批量创建字段规则
    await this.createFieldRules(
      txPrisma,
      materialContractId,
      reqUser,
      processedRules,
      data.contractTemplateId
    );
  }

  // --- 辅助方法 ---
  assignOrgInfo(fieldMap: Map<string, any>, orgInfo: any) {
    const mappings: Record<string, string> = {
      项目名称: 'sealName',
      工程名称: 'name'
    };

    for (const [field, prop] of Object.entries(mappings)) {
      if (fieldMap.has(field)) {
        fieldMap.get(field).value = orgInfo?.[prop];
      }
    }
  }

  assignProjectInfo(fieldMap: Map<string, any>, projectInfo: any[]) {
    // 修复：使用严格比较 === 替代赋值 =
    const fieldMappings: Record<string, string> = {
      工程地址: '工程地址',
      工程概况: '工程概况',
      建设单位: '建设单位',
      项目经理: '项目经理',
      项目经理电话: '项目经理电话'
    };

    // 创建项目信息查找表
    const projectMap = new Map<string, string>();
    projectInfo.forEach((item) => {
      const fieldName = item.basicProjectInfoFieldDetail?.name;
      if (fieldName && fieldMappings[fieldName]) {
        projectMap.set(fieldName, item.value);
      }
    });

    // 赋值到字段
    for (const [field] of Object.entries(fieldMappings)) {
      if (fieldMap.has(field) && projectMap.has(field)) {
        fieldMap.get(field).value = projectMap.get(field);
      }
    }
  }

  assignPartyInfo(
    fieldMap: Map<string, any>,
    partyA: any,
    partyBType: string,
    partyB: any
  ) {
    // 甲方信息
    const partyAMappings: Record<string, string> = {
      甲方名称: 'companyName',
      '统一社会信用代码（甲方）': 'unifiedSocialCreditCode',
      '注册地址（甲方）': 'registeredAddress',
      '开户行账号（甲方）': 'bankAccount',
      '开户银行（甲方）': 'bankName',
      '联系电话（甲方）': 'businessPhone'
    };

    for (const [field, prop] of Object.entries(partyAMappings)) {
      if (fieldMap.has(field)) {
        fieldMap.get(field).value = partyA?.[prop];
      }
    }

    // 乙方信息
    const partyBMappings =
      partyBType === PartyBType.COMPANY
        ? {
            乙方名称: 'companyName',
            '统一社会信用代码（乙方）': 'unifiedSocialCreditCode',
            乙方地址: 'registeredAddress',
            '开户行账号（乙方）': 'bankAccount',
            '收款银行账号（乙方）': 'bankName',
            '联系人电话（乙方）': 'businessPhone',
            '增值税纳税人资格（乙方）': 'taxpayerType'
          }
        : {
            乙方名称: 'fullName',
            '统一社会信用代码（乙方）': 'creditCode',
            乙方地址: 'registeredAddress',
            '联系人（乙方）': 'contactBy',
            '联系人电话（乙方）': 'contactPhone',
            乙方电子邮箱: 'contactEmail',
            '增值税纳税人资格（乙方）': 'taxpayerQualification'
          };

    for (const [field, prop] of Object.entries(partyBMappings)) {
      if (fieldMap.has(field)) {
        let value = partyB?.[prop];

        // 特殊处理纳税人资格
        if (
          field === '增值税纳税人资格（乙方）' &&
          partyBType !== PartyBType.COMPANY
        ) {
          value =
            value === TaxpayerQualification.GENERAL
              ? '一般纳税人'
              : '小规模纳税人';
        }

        fieldMap.get(field).value = value;
      }
    }
  }

  assignTaxRateInfo(fieldMap: Map<string, any>, taxRates: any[]) {
    if (!fieldMap.has('增值税税率') || taxRates.length === 0) return;

    const taxRateSet = new Set<string>();

    taxRates.forEach((item) => {
      item.taxRate.split(',').forEach((tax: string) => {
        taxRateSet.add(
          `${item.name}-${tax}-${dayjs(item.executeDate).format('YYYY/MM/DD')}`
        );
      });
    });

    fieldMap.get('增值税税率').enumValue = Array.from(taxRateSet).join(',');
  }

  async createFieldRules(
    txPrisma: PrismaService,
    materialContractId: string,
    reqUser: IReqUser,
    rules: any[],
    contractTemplateId: string
  ) {
    const { tenantId, orgId, id: userId } = reqUser;

    await txPrisma.materialContractFieldRule.createMany({
      data: rules.map((rule) => ({
        textValue: this.textValueType.includes(rule.fieldType)
          ? rule.value
          : null,
        decimalValue:
          this.decimalValueType.includes(rule.fieldType) ||
          rule.code == '增值税税率'
            ? this.taxRateRegex(rule.value)
            : null,
        tenantId,
        materialContractId,
        contractTemplateId,
        contractTemplateFieldRuleId: rule.contractTemplateFieldRuleId,
        orgId,
        value: rule.value,
        updateBy: userId,
        createBy: userId
      }))
    });
  }

  async updateOne(
    reqUser: IReqUser,
    id: string,
    data: MaterialContractUpdateDto
  ) {
    const { orgId, tenantId, id: userId } = reqUser;
    // 查询当前合同
    const contract = await this.getOne(id, reqUser);
    // 唯一性校验
    await this.checkUnique(
      tenantId,
      orgId,
      data.name,
      data.code,
      id,
      contract.parentId
    );
    const contractInfo = await this.prisma.materialContract.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: userId
      },
      select: { id: true, name: true }
    });

    // 如果是暂定合同，更新一下进场验收单的合同名称
    if (
      data.proposedStatus === ProposedStatus.PROVISIONAL &&
      contractInfo?.name !== data.name
    ) {
      await this.prisma.materialIncomingInspection.updateMany({
        where: {
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          contractId: id
        },
        data: {
          contractName: data.name
        }
      });
    }

    return true;
  }

  async deleteOne(reqUser: IReqUser, id: string) {
    // 删除前校验
    await this.checkBeforeDelete(reqUser, id);
    await this.prisma.$transaction(async (txPrisma) => {
      // 合同
      await txPrisma.materialContract.update({
        where: {
          id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 字段详情
      await txPrisma.materialContractFieldRule.updateMany({
        where: {
          materialContractId: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 合同清单(消耗材料)
      await txPrisma.contractConsumeMaterialDetails.updateMany({
        where: {
          materialContractId: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 合同清单(商品混凝土材料)
      await txPrisma.contractConcreteDetails.updateMany({
        where: {
          materialContractId: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 合同清单(租赁周转材料)
      await txPrisma.contractTurnoverMaterialDetails.updateMany({
        where: {
          materialContractId: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 合同 - 单位换算
      await txPrisma.materialContractUnitCalculation.updateMany({
        where: {
          materialContractId: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 合同 - 附件
      await txPrisma.materialContractAccessory.updateMany({
        where: {
          materialContractId: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
      // 合同附加费（商品混凝土材料）
      await txPrisma.contractConcreteSurcharge.updateMany({
        where: {
          materialContractId: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id
        }
      });
    });
    return true;
  }

  async checkBeforeDelete(reqUser: IReqUser, id: string) {
    const { orgId, tenantId } = reqUser;

    // 查询合同
    const contract = await this.getOne(id, reqUser);
    if (contract.submitStatus === SubmitStatus.SUBMITTED) {
      throw new BadRequestException('合同已提交，不可删除');
    }
    if (contract.auditStatus === AuditStatus.APPROVED) {
      throw new BadRequestException('合同已审核通过，不可删除');
    }
    // 查询是否被进场验收单引用
    const incomingInspection =
      await this.prisma.materialIncomingInspection.findFirst({
        select: {
          id: true
        },
        where: {
          contractId: contract.id,
          tenantId,
          orgId,
          isDeleted: false
        }
      });
    if (incomingInspection) {
      throw new HttpException(
        '合同已被进场验收单引用，不可删除',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async getOne(id: string, reqUser: IReqUser) {
    const template = await this.prisma.materialContract.findUnique({
      where: {
        id,
        isDeleted: false,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId
      }
    });
    if (!template) {
      throw new BadRequestException('合同不存在');
    }
    return template;
  }

  // async initRuleField(
  //   txPrisma: PrismaService,
  //   id: string,
  //   reqUser: IReqUser,
  //   data: SaveRuleFieldDto[],
  //   contractTemplateId: string
  // ) {
  //   const { orgId, tenantId, id: userId } = reqUser;
  //   await txPrisma.materialContractFieldRule.createMany({
  //     data: data.map((item) => ({
  //       tenantId,
  //       materialContractId: id,
  //       contractTemplateId: contractTemplateId,
  //       contractTemplateFieldRuleId: item.contractTemplateFieldRuleId,
  //       orgId: orgId,
  //       value: item.value,
  //       updateBy: userId,
  //       createBy: userId
  //     }))
  //   });
  // }

  async saveRuleField(id: string, reqUser: IReqUser, data: SaveRuleFieldDto[]) {
    const { orgId, tenantId, id: userId } = reqUser;
    // 查询合同信息
    const template = await this.getOne(id, reqUser);
    // 查询所有保存的数据
    const fieldRuleList = await this.prisma.materialContractFieldRule.findMany({
      where: {
        orgId: reqUser.orgId,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        materialContractId: id
      }
    });
    // 保存合同的价格类型
    await this.savePriceType(reqUser, id, data, template.contractTemplateId);
    await this.prisma.$transaction(async (tx) => {
      await Promise.all(
        data.map(async (item) => {
          const isExist = fieldRuleList.find(
            (record) =>
              record.materialContractId === id &&
              record.orgId === orgId &&
              record.contractTemplateFieldRuleId ===
                item.contractTemplateFieldRuleId
          );
          let textValue = null;
          if (item.code === '增值税税率') {
            textValue = item.defaultValue;
          }
          if (this.textValueType.includes(item.fieldType)) {
            textValue = item.value;
          }
          if (!isExist) {
            await tx.materialContractFieldRule.create({
              data: {
                tenantId,
                materialContractId: id,
                contractTemplateId: template.contractTemplateId,
                contractTemplateFieldRuleId: item.contractTemplateFieldRuleId,
                orgId: orgId,
                textValue: textValue,
                decimalValue:
                  this.decimalValueType.includes(item.fieldType) ||
                  item.code === '增值税税率'
                    ? this.taxRateRegex(item.value)
                    : null,
                updateBy: userId,
                createBy: userId
              }
            });
          } else {
            await tx.materialContractFieldRule.updateMany({
              where: {
                orgId: reqUser.orgId,
                tenantId: reqUser.tenantId,
                materialContractId: id,
                contractTemplateFieldRuleId: item.contractTemplateFieldRuleId,
                contractTemplateId: template.contractTemplateId
              },
              data: {
                textValue: textValue,
                decimalValue:
                  this.decimalValueType.includes(item.fieldType) ||
                  item.code === '增值税税率'
                    ? this.taxRateRegex(item.value)
                    : null,
                updateBy: userId
              }
            });
          }
        })
      );
      // 重新计算明细项
      await this.contractMaterialDetailService.calculation(
        tx as PrismaService,
        reqUser,
        id,
        template.contractTemplateId,
        this.taxRateRegex(
          data.find((item) => item.code === '增值税税率')?.value
        )
      );
    });
    return true;
  }

  async savePriceType(
    reqUser: IReqUser,
    id: string,
    data: SaveRuleFieldDto[],
    contractTemplateId: string
  ) {
    // 获取价格类型的字段
    const field = await this.prisma.$queryRaw<any[]>`
      select 
        ctfr."id" from field_rule fr
        
        join contract_template_field_rule ctfr
        
        on ctfr.field_rule_id = fr.id and ctfr.contract_template_id = ${contractTemplateId} and ctfr.is_deleted = false
        
      where fr.code = '单价类型' and fr.is_deleted = false
    `;
    if (field.length) {
      const obj = data.find(
        (item) => item.contractTemplateFieldRuleId === field[0].id
      );
      await this.prisma.materialContract.update({
        where: {
          id,
          isDeleted: false
        },
        data: {
          priceType: obj?.value,
          updateBy: reqUser.id
        }
      });
    }
  }

  async getRuleFieldList(req: Request, id: string, reqUser: IReqUser) {
    const { orgId, tenantId } = reqUser;
    // 查询合同信息
    const template = await this.getOne(id, reqUser);
    let ruleList = await this.prisma.$queryRaw<any[]>`
        with tmp_classify as (
					select 
              '1' as id, null as code, '项目信息' as name, 1 as sort, null as parent_id, 1 as level,
              'PROJECT_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value,  false as is_matching,
							 null as coord, null as value, null as default_value,
               false as is_default_required, false as is_update
              
            union all
            
            select 
              '2' as id, null as code, '甲方信息' as name, 2 as sort, null as parent_id, 1 as level,
              'Party_A_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value,  false as is_matching,
							 null as coord, null as value, null as default_value,
               false as is_default_required, false as is_update

            union all
            
            select 
						 '3' as id, null as code, '乙方信息' as name, 3 as sort, null as parent_id, 1 as level,
							'Party_B_INFORMATION' as classify, false as is_required, null as field_type,
							null as enum_value,  false as is_matching,
							 null as coord, null as value, null as default_value,
               false as is_default_required, false as is_update
              
            union all
            
            select 
              '4' as id, null as code, '合同信息' as name, 4 as sort, null as parent_id, 1 as level,
              'CONTRACT_INFORMATION' as classify, false as is_required, null as field_type,
              null as enum_value,  false as is_matching, null as coord, null as value, null as default_value,
              false as is_default_required, false as is_update
							
          ), tmp_field_rule as (
          SELECT
              ctfr.id,
              esct.code,
              esct.name,
              esct.sort,
              tc.id AS parent_id, 
              2 as level,
              esct.classify::TEXT AS classify,
              ctfr.is_required,
							esct.field_type::TEXT AS field_type,
							esct.enum_value,
              esct.is_matching,
							ctfr.coord,
              case 
                when esct.code != '增值税税率' and ccfr.text_value is not null then ccfr.text_value
                when ccfr.decimal_value is not null THEN CAST(ccfr.decimal_value AS TEXT)
                else null end AS value,
              ccfr.text_value as default_value,
              esct.is_default_required,
              esct.is_update
            from contract_template_field_rule ctfr 
            left JOIN material_contract_field_rule ccfr
              on ctfr.id = ccfr.contract_template_field_rule_id
              and ctfr.contract_template_id = ccfr.contract_template_id
              and ccfr.is_deleted = false
              and ccfr.contract_template_id = ${template.contractTemplateId}
              and ccfr.org_id = ${orgId}
              and ccfr.tenant_id = ${tenantId}
              and ccfr.material_contract_id = ${id}
            JOIN field_rule esct
              on esct.is_deleted = false
              and esct.id = ctfr.field_rule_id
            JOIN tmp_classify tc on esct.classify::TEXT = tc.classify
            where ctfr.is_deleted = false and ctfr.contract_template_id = ${template.contractTemplateId}   
            order by esct.sort, esct.create_at DESC
          ) 
          
          select
            id, code, name, parent_id, is_required, is_matching, enum_value, value, default_value, coord, is_default_required, is_update, field_type
          from (
            select * from tmp_classify

            union all

            select * from tmp_field_rule
          ) as t
					WHERE (t.level = 1 AND EXISTS (SELECT 1 FROM tmp_field_rule WHERE parent_id = t.id))
						 OR t.level = 2
					ORDER BY level, sort, name
      `;
    // 获取税率信息
    ruleList = await this.getTaxRateInfo(orgId, tenantId, ruleList);
    // 获取列表信息
    ruleList = await this.getDetailInfo(id, reqUser, ruleList);
    return ruleList.map((item) => {
      return {
        ...item,
        defaultValue:
          (this.decimalValueType.includes(item.fieldType) ||
            item.code === '增值税税率') &&
          item.defaultValue
            ? item.defaultValue
            : '',
        value:
          (this.decimalValueType.includes(item.fieldType) ||
            item.code === '增值税税率') &&
          item.value
            ? Decimal(item.value).toNumber()
            : item.value
      };
    });
  }

  async getDetailInfo(id: string, reqUser: IReqUser, ruleList: any[]) {
    const { orgId, tenantId } = reqUser;
    // 查询模版的分类
    const contract = await this.prisma.materialContract.findUnique({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      select: {
        contractTemplate: {
          select: {
            classify: true
          }
        }
      }
    });
    const contractTemplateType = contract?.contractTemplate?.classify;
    for (const item of ruleList) {
      if (item.code === '货物清单表（采购）') {
        item.value =
          await this.contractMaterialDetailService.getChooseMaterialDetail(
            reqUser,
            {
              contractTemplateType:
                contractTemplateType as ContractTemplateClassifyType
            },
            id
          );
      }
      if (item.code === '货物清单表（商混）') {
        item.value =
          await this.contractMaterialDetailService.getChooseMaterialDetail(
            reqUser,
            {
              contractTemplateType:
                contractTemplateType as ContractTemplateClassifyType
            },
            id
          );
      }
      if (item.code === '货物清单表（租赁）') {
        item.value =
          await this.contractMaterialDetailService.getChooseMaterialDetail(
            reqUser,
            {
              contractTemplateType:
                contractTemplateType as ContractTemplateClassifyType
            },
            id
          );
      }
    }
    return ruleList;
  }

  // 获取税率信息
  async getTaxRateInfo(orgId: string, tenantId: string, ruleList: any[]) {
    // 获取税率信息
    const taxRateInfo = await this.prisma.$queryRaw<any[]>`
      select
          mde.tax_rate, mde.execute_date, mde.name
        from account_taxrate_dictionary_detail amde
        join taxrate_dictionary_detail mde
        on mde.id = amde.detail_id
          and mde.is_deleted = false
          and mde.is_active = true
				join taxrate_dictionary_category tdc
				on tdc.id = mde.taxrate_dictionary_category_id
					and tdc.is_deleted = false
					and tdc.is_active = true
        where amde.is_deleted = false
          and amde.tenant_id = ${tenantId}
          and amde.org_id = ${orgId}
				ORDER BY 
					tdc.is_active desc, 
					tdc."level" asc, 
					tdc.sort asc, 
					mde.is_active desc, 
					mde.sort asc
    `;
    if (!taxRateInfo.length) {
      throw new HttpException('成本核算税率版本未设置', HttpStatus.BAD_REQUEST);
    }
    const taxRateList: { label: string; value: string }[] = [];
    if (taxRateInfo.length) {
      taxRateInfo.map((item) => {
        item.taxRate.split(',').forEach((tax: string) => {
          const labelList = [item.name, tax];
          if (item.executeDate) {
            labelList.push(dayjs(item.executeDate).format('YYYY-MM-DD'));
          }
          const label = labelList.join('-');
          if (!taxRateList.find((taxRate) => taxRate.label === label)) {
            taxRateList.push({
              label,
              value: tax
            });
          }
        });
      });
      ruleList.map((item) => {
        if (item.code === '增值税税率') item.enumValue = taxRateList;
      });
    }
    return ruleList;
  }

  // 新增校验
  async checkUnique(
    tenantId: string,
    orgId: string,
    name: string,
    code: string,
    id: string | null = null,
    parentId: string | null = null
  ) {
    const nameData = await this.prisma.materialContract.findFirst({
      select: {
        id: true
      },
      where: {
        id: {
          not: id ? id : undefined
        },
        name,
        orgId: orgId,
        tenantId: tenantId,
        isDeleted: false
      }
    });
    if (nameData) {
      throw new HttpException(
        `${parentId ? '补充协议名称已存在' : '合同名称已存在'}`,
        HttpStatus.BAD_REQUEST
      );
    }
    const codeData = await this.prisma.materialContract.findFirst({
      select: {
        id: true
      },
      where: {
        id: {
          not: id ? id : undefined
        },
        code,
        orgId: orgId,
        tenantId: tenantId,
        isDeleted: false
      }
    });
    if (codeData) {
      throw new HttpException(
        `${parentId ? '补充协议编码已存在' : '合同编码已存在'}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async editSubmitStatus(
    id: string,
    reqUser: IReqUser,
    data: MaterialContractUpdateSubmitStatusDto
  ) {
    const { submitStatus } = data;
    const { tenantId, orgId, id: userId } = reqUser;
    // 查询当前合同信息
    const contract = await this.getOne(id, reqUser);
    let taxRate, signDate;
    // 检验合同是否被进场验收单引用
    if (submitStatus === SubmitStatus.PENDING) {
      // 取消提交前校验
      await this.checkBeforeUnSubmit(
        contract.proposedStatus,
        tenantId,
        orgId,
        id
      );
    } else {
      // 提交前校验，校验必填字段是否都填写了
      await this.checkRequiredField(
        id,
        reqUser,
        contract.contractTemplateId,
        'error'
      );
      // 校验明细必填字段是否都填写了
      await this.checkDetailRequiredField(id, reqUser);
      // 获取增值税税率和签约日期
      const obj = await this.getTaxRateAndSignDate(id, reqUser);
      taxRate = obj.taxRate;
      signDate = obj.signDate;
    }

    await this.prisma.materialContract.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        submitStatus,
        taxRate: submitStatus === SubmitStatus.PENDING ? null : taxRate,
        signDate: submitStatus === SubmitStatus.PENDING ? null : signDate,
        updateBy: userId
      }
    });
    return true;
  }

  /**
   * 查询税率
   * @param id 合同id
   */
  async getTaxRateAndSignDate(id: string, reqUser: IReqUser) {
    // 查询合同
    const contract = await this.prisma.materialContract.findUnique({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      }
    });
    const fieldCode = ['增值税税率', '签约日期'];
    const res = await this.prisma.$queryRaw<any[]>`
      select 
        fr.code, mcfr.decimal_value, mcfr.text_value from field_rule fr
        join contract_template_field_rule ctfr
        on ctfr.field_rule_id = fr.id 
          and ctfr.contract_template_id = ${contract?.contractTemplateId}
          and ctfr.is_deleted = false
        join material_contract_field_rule mcfr
          on mcfr.contract_template_field_rule_id = ctfr."id"
          and mcfr.is_deleted = false
          and mcfr.org_id = ${reqUser.orgId}
          and mcfr.tenant_id = ${reqUser.tenantId}
          and mcfr.material_contract_id = ${id}
        where fr.code in (${Prisma.join(fieldCode)}) and fr.is_deleted = false
    `;
    const taxRate = res.find((item) => item.code === '增值税税率').decimalValue;
    const signDate = res.find((item) => item.code === '签约日期').textValue;
    return {
      taxRate,
      signDate: new Date(signDate)
    };
  }

  // 校验明细必填字段是否都填写了
  async checkDetailRequiredField(id: string, reqUser: IReqUser) {
    const { tenantId, orgId } = reqUser;
    // 获取范本类型
    const contract = await this.getContractTemplate(id, reqUser);
    const templateClassifyType = contract?.contractTemplate.classify;
    // 根据范本类型校验查询明细必填字段是否都填写了
    if (
      templateClassifyType === ContractTemplateClassifyType.MATERIALS_PURCHASING
    ) {
      // 物资采购合同
      // 查询明细
      const details = await this.prisma.contractConsumeMaterialDetails.findMany(
        {
          where: {
            materialContractId: id,
            isDeleted: false,
            tenantId,
            orgId,
            OR: [
              {
                changeQuantity: null
              },
              {
                changePriceIncludingTax: null
              }
            ]
          }
        }
      );
      if (details.length) {
        throw new BadRequestException(
          '货物清单有必填字段未填写，请填写必填字段!'
        );
      }
    }
    if (
      templateClassifyType ===
      ContractTemplateClassifyType.MATERIALS_COMMERCIAL_CONCRETE
    ) {
      // 商品混凝土
      // 查询明细
      const details = await this.prisma.contractConcreteDetails.findMany({
        where: {
          materialContractId: id,
          isDeleted: false,
          tenantId,
          orgId,
          OR: [
            {
              changeQuantity: null
            },
            {
              changePriceIncludingTax: null
            }
          ]
        }
      });
      if (details.length) {
        throw new BadRequestException(
          '货物清单有必填字段未填写，请填写必填字段!'
        );
      }
    }
    if (
      templateClassifyType ===
      ContractTemplateClassifyType.MATERIALS_LEASING_TURNOVER
    ) {
      // 租赁
      // 查询明细
      const details =
        await this.prisma.contractTurnoverMaterialDetails.findMany({
          where: {
            materialContractId: id,
            isDeleted: false,
            tenantId,
            orgId,
            OR: [
              {
                changeQuantity: null
              },
              {
                changePriceIncludingTax: null
              },
              {
                changeProvisionalDays: null
              }
            ]
          }
        });
      if (details.length) {
        throw new BadRequestException(
          '货物清单有必填字段未填写，请填写必填字段!'
        );
      }
    }
  }

  // 校验合同必填字段是否都填写了
  async checkRequiredField(
    id: string,
    reqUser: IReqUser,
    contractTemplateId: string,
    type: 'boolean' | 'error'
  ) {
    // 查询合同字段是否已经全部都填写了
    const data = await this.prisma.$queryRaw<any[]>`
      select 
        case 
          when mcfr.text_value is not null then mcfr.text_value
          when mcfr.decimal_value is not null THEN CAST(mcfr.decimal_value AS TEXT)
          else null end AS value
        from material_contract_field_rule mcfr
        join contract_template_field_rule ctfr
        on ctfr.contract_template_id = ${contractTemplateId}
          and ctfr.is_deleted = false
          and ctfr.tenant_id = mcfr.tenant_id
          and ctfr."id" = mcfr.contract_template_field_rule_id
        join field_rule fr
        on fr."id" = ctfr.field_rule_id
          and fr.is_deleted = false
          and fr.is_required = true
        where mcfr.is_deleted = false
        and mcfr.tenant_id = ${reqUser.tenantId}
        and mcfr.org_id = ${reqUser.orgId}
        and mcfr.material_contract_id = ${id}
    `;
    if (type === 'boolean') {
      return data.filter((item) => !item.value).length ? false : true;
    } else {
      for (const element of data) {
        if (!element.value) {
          throw new BadRequestException('有合同必填字段项未填写，请先前往填写');
        }
      }
    }
  }

  // 查询合同范本的类型
  async getContractTemplate(contractId: string, reqUser: IReqUser) {
    const contractTemplate = await this.prisma.materialContract.findUnique({
      where: {
        id: contractId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      select: {
        contractTemplateId: true,
        contractTemplate: {
          select: {
            classify: true
          }
        }
      }
    });
    return contractTemplate;
  }

  // 计算统计价格
  async calculatePrice(id: string, reqUser: IReqUser) {
    // 查询合同范本类型
    const contractTemplate = await this.getContractTemplate(id, reqUser);
    let changeTotalPriceIncludingTax: Decimal = new Decimal(0);
    let changeTotalPriceExcludingTax: Decimal = new Decimal(0);
    let changeTotalValueAddedTaxAmount: Decimal = new Decimal(0);
    const classify = contractTemplate?.contractTemplate.classify;
    if (classify === ContractTemplateClassifyType.MATERIALS_PURCHASING) {
      // 计算明细
      const detail = await this.prisma.contractConsumeMaterialDetails.aggregate(
        {
          _sum: {
            changeTotalPriceIncludingTax: true,
            changeTotalPriceExcludingTax: true,
            changeTotalValueAddedTaxAmount: true
          },
          where: {
            materialContractId: id,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            isDeleted: false
          }
        }
      );
      changeTotalPriceIncludingTax =
        detail._sum.changeTotalPriceIncludingTax || new Decimal(0);
      changeTotalPriceExcludingTax =
        detail._sum.changeTotalPriceExcludingTax || new Decimal(0);
      changeTotalValueAddedTaxAmount =
        detail._sum.changeTotalValueAddedTaxAmount || new Decimal(0);
    }
    if (
      classify === ContractTemplateClassifyType.MATERIALS_COMMERCIAL_CONCRETE
    ) {
      const detail = await this.prisma.contractConcreteDetails.aggregate({
        _sum: {
          changeTotalPriceIncludingTax: true,
          changeTotalPriceExcludingTax: true,
          changeTotalValueAddedTaxAmount: true
        },
        where: {
          materialContractId: id,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false
        }
      });
      changeTotalPriceIncludingTax =
        detail._sum.changeTotalPriceIncludingTax || new Decimal(0);
      changeTotalPriceExcludingTax =
        detail._sum.changeTotalPriceExcludingTax || new Decimal(0);
      changeTotalValueAddedTaxAmount =
        detail._sum.changeTotalValueAddedTaxAmount || new Decimal(0);
    }
    // 修改合同暂定价
    await this.prisma.materialContract.update({
      data: {
        amount: changeTotalPriceIncludingTax,
        updateBy: reqUser.id,
        updateAt: new Date()
      },
      where: {
        id,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
    // 定义字段映射关系
    const codeList = [
      '合同含税总金额（小写，元）',
      '合同不含税总金额（小写，元）',
      '合同增值税金额（小写，元）',
      '合同含税总金额（大写，元）',
      '合同不含税总金额（大写，元）'
    ];
    // 准备要更新的数据
    const ruleList = await this.prisma.$queryRaw<any[]>`
      SELECT
          mcfr.id,
          esct.code
				from material_contract_field_rule mcfr
				join contract_template_field_rule ctfr 
					on ctfr.contract_template_id = mcfr.contract_template_id
					and ctfr.id = mcfr.contract_template_field_rule_id
					and ctfr.is_deleted = false
        JOIN field_rule esct
          on esct.is_deleted = false
          and esct.id = ctfr.field_rule_id
					and esct.code = ANY(${codeList}::text[])
        where mcfr.material_contract_id = ${id}
					and mcfr.tenant_id = ${reqUser.tenantId}
					and mcfr.org_id = ${reqUser.orgId}
					and mcfr.is_deleted = false
    `;
    // 2. 生成待更新的数据对象
    const updateDataMap = new Map();
    ruleList.forEach((item) => {
      if (item.code === '合同含税总金额（小写，元）')
        updateDataMap.set(item.id, {
          id: item.id,
          value: changeTotalPriceIncludingTax.toFixed(2),
          updateBy: reqUser.id,
          fieldType: FieldType.NUMBER
        });
      if (item.code === '合同不含税总金额（小写，元）')
        updateDataMap.set(item.id, {
          id: item.id,
          value: changeTotalPriceExcludingTax.toFixed(2),
          updateBy: reqUser.id,
          fieldType: FieldType.NUMBER
        });
      if (item.code === '合同增值税金额（小写，元）')
        updateDataMap.set(item.id, {
          id: item.id,
          value: changeTotalValueAddedTaxAmount.toFixed(2),
          updateBy: reqUser.id,
          fieldType: FieldType.NUMBER
        });
      if (item.code === '合同含税总金额（大写，元）')
        updateDataMap.set(item.id, {
          id: item.id,
          value: Utils.numberToFinancialChinese(
            changeTotalPriceIncludingTax.toFixed(2)
          ),
          updateBy: reqUser.id,
          fieldType: FieldType.TEXT
        });
      if (item.code === '合同不含税总金额（大写，元）')
        updateDataMap.set(item.id, {
          id: item.id,
          value: Utils.numberToFinancialChinese(
            changeTotalPriceExcludingTax.toFixed(2)
          ),
          updateBy: reqUser.id,
          fieldType: FieldType.TEXT
        });
    });
    // 3. 批量更新数据
    await Promise.all(
      Array.from(updateDataMap.values()).map((data) => {
        return this.prisma.materialContractFieldRule.update({
          where: { id: data.id, isDeleted: false },
          data: {
            textValue: data.fieldType === FieldType.TEXT ? data.value : null,
            decimalValue:
              data.fieldType === FieldType.NUMBER ? data.value : null,
            updateBy: data.updateBy
          }
        });
      })
    );
    return true;
  }

  async checkBeforeUnSubmit(
    proposedStatus: ProposedStatus,
    tenantId: string,
    orgId: string,
    id: string
  ) {
    // 查询是否已审核
    const audit = await this.prisma.materialContract.findUnique({
      select: {
        id: true,
        auditStatus: true
      },
      where: {
        id,
        tenantId,
        orgId,
        isDeleted: false
      }
    });
    if (audit?.auditStatus === AuditStatus.APPROVED) {
      throw new HttpException(
        '合同已审核通过，不可取消提交',
        HttpStatus.BAD_REQUEST
      );
    }
    // 查询是否被进场验收单引用
    const incomingInspection =
      await this.prisma.materialIncomingInspection.findFirst({
        select: {
          id: true
        },
        where: {
          contractId: id,
          tenantId,
          orgId,
          isDeleted: false
        }
      });
    if (incomingInspection && proposedStatus !== ProposedStatus.PROVISIONAL) {
      throw new HttpException(
        '合同已被进场验收单引用，不可取消提交',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  async editAuditStatus(
    id: string,
    reqUser: IReqUser,
    data: MaterialContractUpdateAuditStatusDto
  ) {
    const { auditStatus } = data;
    const { tenantId, orgId, id: userId } = reqUser;
    // 查询当前合同信息
    const contract = await this.getOne(id, reqUser);
    // 检验合同下是否有补充协议
    if (
      (auditStatus === AuditStatus.PENDING ||
        auditStatus === AuditStatus.REJECTED) &&
      auditStatus !== contract.auditStatus
    ) {
      await this.checkBeforeAudit(contract, tenantId, orgId, auditStatus);
    }
    if (auditStatus === AuditStatus.APPROVED && contract.parentId) {
      // 为补充协议则修改父级最终供应商和甲方名称
      await this.prisma.materialContract.update({
        where: {
          id: contract.parentId,
          isDeleted: false
        },
        data: {
          partyAEndName: contract.partyAEndName,
          partyBEndName: contract.partyBEndName,
          updateBy: userId
        }
      });
    }
    await this.prisma.materialContract.update({
      where: {
        id,
        isDeleted: false
      },
      data: {
        auditStatus,
        updateBy: userId
      }
    });
    return true;
  }

  async checkBeforeAudit(
    contract: any,
    tenantId: string,
    orgId: string,
    auditStatus: AuditStatus
  ) {
    if (contract.parentId) {
      // 如果是补充协议，则校验补充协议是否可取消审核
      // 查询创建时间大于当前补充协议的补充协议，且已审核，若存在则当前补充协议不可取消审核
      const approvedChild = await this.prisma.materialContract.findFirst({
        where: {
          parentId: contract.parentId,
          id: {
            not: contract.id
          },
          isDeleted: false,
          tenantId,
          orgId,
          createAt: {
            gt: dayjs(contract.createAt).add(8, 'hour').toDate()
          }
        }
      });
      if (approvedChild) {
        throw new BadRequestException(
          `已有最新的补充协议，不允许${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
      // 根据补充协议的审核通过时间和进场验收单的创建时间进行对比
      const materialIncomingInspection =
        await this.prisma.materialIncomingInspection.findFirst({
          select: {
            id: true
          },
          where: {
            contractId: contract.parentId,
            tenantId,
            orgId,
            isDeleted: false,
            createAt: {
              gt: dayjs(contract.updateAt).add(8, 'hour').toDate()
            }
          }
        });
      if (materialIncomingInspection) {
        throw new BadRequestException(
          `补充协议已被进场验收单使用，不允许${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
    } else {
      // 查询父级合同信息下是否有补充协议
      const child = await this.prisma.materialContract.findFirst({
        where: {
          parentId: contract.id,
          isDeleted: false,
          tenantId,
          orgId
        }
      });
      if (child) {
        throw new BadRequestException(
          `该合同下有补充协议，不允许${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
      // 查询是否被进场验收单引用
      const incomingInspection =
        await this.prisma.materialIncomingInspection.findFirst({
          select: {
            id: true
          },
          where: {
            contractId: contract.id,
            tenantId,
            orgId,
            isDeleted: false
          }
        });
      if (incomingInspection) {
        throw new HttpException(
          `合同已被进场验收单引用，不可${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`,
          HttpStatus.BAD_REQUEST
        );
      }
    }
  }

  async export(req: Request, reqUser: IReqUser) {
    const data = await this.getList(req, reqUser, {});
    const proposedStatus = {
      OFFICIAL: '正式合同',
      PROVISIONAL: '暂存合同'
    };
    const submitStatus = {
      PENDING: '未提交',
      SUBMITTED: '已提交'
    };
    const auditStatus = {
      PENDING: '待审核',
      AUDITING: '审批中',
      APPROVED: '审核通过',
      REJECTED: '审核拒绝'
    };
    const fulfillmentStatus = {
      NOT_STARTED: '未开始',
      IN_PROGRESS: '履约中',
      COMPLETED: '已完成'
    };

    const exportData = data.map((item) => {
      return {
        ...item,
        proposedStatus:
          proposedStatus[item.proposedStatus as keyof typeof ProposedStatus],
        submitStatus:
          submitStatus[item.submitStatus as keyof typeof submitStatus],
        auditStatus: auditStatus[item.auditStatus as keyof typeof auditStatus],
        fulfillmentStatus:
          fulfillmentStatus[
            item.fulfillmentStatus as keyof typeof fulfillmentStatus
          ]
      };
    });

    // 创建流式工作簿
    const workbook = new ExcelJS.Workbook();
    // 创建工作表
    const worksheet = workbook.addWorksheet('sheet1', {
      views: [{ state: 'frozen', xSplit: 0, ySplit: 1 }], // 冻结表头
      pageSetup: { fitToPage: true, fitToWidth: 1, fitToHeight: 100 }
    });

    // 定义列
    worksheet.columns = [
      { header: '履约状态', key: 'fulfillmentStatus', width: 10 },
      { header: '合同名称', key: 'name', width: 32 },
      { header: '合同编码', key: 'code', width: 32 },
      { header: '供应商简称', key: 'partyBName', width: 32 },
      { header: '签约主体', key: 'partyAName', width: 32 },
      { header: '价格类型', key: 'priceType', width: 10 },
      { header: '暂定合同金额', key: 'amount', width: 10 },
      { header: '合同编制', key: 'contractTemplateName', width: 32 },
      { header: '编制日期', key: 'createAt', width: 32 },
      { header: '编制人', key: 'creator', width: 15 },
      { header: '拟定状态', key: 'proposedStatus', width: 10 },
      { header: '提交状态', key: 'submitStatus', width: 10 },
      { header: '审核状态', key: 'auditStatus', width: 10 }
    ];

    // 表头样式
    const headerStyle: Partial<ExcelJS.Style> = {
      font: { bold: true, color: { argb: 'FFFFFFFF' } },
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF0070C0' }
      },
      alignment: { horizontal: 'center' }
    };

    // 应用表头样式
    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.style = headerStyle;
    });

    // 添加数据行并提交
    for (const element of exportData) {
      worksheet.addRow({
        fulfillmentStatus: element.fulfillmentStatus,
        name: element.name,
        code: element.code,
        partyAName: element.partyAName,
        partyBName: element.partyBName,
        priceType: element.priceType,
        amount: element.amount,
        contractTemplateName: element.contractTemplateName,
        createAt: element.createAt,
        creator: element.creator,
        proposedStatus: element.proposedStatus,
        submitStatus: element.submitStatus,
        auditStatus: element.auditStatus
      });
    }
    return await workbook.xlsx.writeBuffer();
  }

  // 正则税率字符串
  private taxRateRegex(str?: string | null) {
    return str ? Decimal(str) : null;
  }

  /**
   * 修改合同履约状态
   * @param reqUser
   * @param contractId 合同id
   * @param fulfillmentStatus 履约状态（）
   */
  async editContractFulfillmentStatus(
    reqUser: IReqUser,
    contractId: string | null,
    fulfillmentStatus?: FulfillmentStatus
  ) {
    if (contractId) {
      // 修改合同履约状态
      await this.prisma.$transaction(async (txPrisma) => {
        // 合同
        await txPrisma.materialContract.update({
          where: {
            id: contractId,
            isDeleted: false,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId
          },
          data: {
            fulfillmentStatus,
            updateBy: reqUser.id
          }
        });
        // 补充协议
        await txPrisma.materialContract.updateMany({
          where: {
            parentId: contractId,
            isDeleted: false,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId
          },
          data: {
            fulfillmentStatus,
            updateBy: reqUser.id
          }
        });
      });
    }
  }
}
