import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { MaterialReceivingAttachmentCreateDto } from './material-receiving-attachment.dto';

@Injectable()
export class MaterialReceivingAttachmentService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(receivingId: string, reqUser: IReqUser) {
    return await this.prisma.materialReceivingAttachment.findMany({
      where: {
        receivingId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }

  async add(data: MaterialReceivingAttachmentCreateDto, reqUser: IReqUser) {
    return await this.prisma.materialReceivingAttachment.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id
      }
    });
  }

  async delete(fileKey: string, reqUser: IReqUser) {
    return await this.prisma.materialReceivingAttachment.updateMany({
      where: {
        fileKey,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }
}
