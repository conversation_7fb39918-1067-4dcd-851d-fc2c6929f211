import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { IReqUser } from '@/common/interfaces/req-user.interface';

import {
  MaterialReceivingAttachmentCreateDto,
  MaterialReceivingAttachmentResDto
} from './material-receiving-attachment.dto';
import { MaterialReceivingAttachmentService } from './material-receiving-attachment.service';

@ApiTags('收料单/附件')
@Controller('material-receiving-attachment')
export class MaterialReceivingAttachmentController {
  constructor(private readonly service: MaterialReceivingAttachmentService) {}

  @ApiOperation({
    summary: '获取附件列表',
    description: '获取附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功',
    type: MaterialReceivingAttachmentResDto,
    isArray: true
  })
  @Get('/:receivingId')
  async getList(
    @Param('receivingId') receivingId: string,
    @ReqUser() reqUser: IReqUser
  ) {
    return await this.service.getList(receivingId, reqUser);
  }

  @ApiOperation({
    summary: '新增附件列表',
    description: '新增附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '新增附件列表成功'
  })
  @Post()
  async add(
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReceivingAttachmentCreateDto
  ) {
    return await this.service.add(data, reqUser);
  }

  @ApiOperation({
    summary: '删除附件列表',
    description: '删除附件列表'
  })
  @ApiResponse({
    status: 200,
    description: '获取附件列表成功'
  })
  @Delete('/:fileKey')
  async delete(
    @ReqUser() reqUser: IReqUser,
    @Param('fileKey') fileKey: string
  ) {
    return await this.service.delete(fileKey, reqUser);
  }
}
