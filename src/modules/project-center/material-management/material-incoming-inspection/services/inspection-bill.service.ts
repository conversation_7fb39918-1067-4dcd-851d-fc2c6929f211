import {
  BadRequestException,
  Injectable,
  InternalServerErrorException
} from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as dayjs from 'dayjs';

import { AuditStatusText } from '@/common/constants/common.constant';
import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ExcelService } from '@/modules/platform/excel.service';
import { PlatformService } from '@/modules/platform/platform.service';
import { AuditStatus, SubmitStatus } from '@/prisma/generated';
import { PurchaseType } from '@/prisma/generated/enums';

import { MaterialContractService } from '../../material-contract/material-contract/material-contract.service';
import {
  InspectionBillListResponseDto,
  QueryInspectionBillListDto,
  UpdateInspectionBillListDto
} from '../material-incoming-inspection.dto';
import { MaterialIncomingInspectionRepository } from '../repositories/inspection-bill.repository';

@Injectable()
export class MaterialIncomingInspectionListService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly repository: MaterialIncomingInspectionRepository,
    private readonly excelService: ExcelService,
    private readonly platformService: PlatformService
  ) {}

  async getTimeList(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.materialIncomingInspection.findMany({
      select: {
        year: true,
        month: true,
        day: true
      },
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          parentId: null,
          year: time.year,
          month: time.month,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}`].count += 1;

      // 添加子级 年_月_日
      if (!resultMap[`${time.year}_${time.month}_${time.day}`]) {
        resultMap[`${time.year}_${time.month}_${time.day}`] = {
          id: `${time.year}_${time.month}_${time.day}`,
          parentId: `${time.year}_${time.month}`,
          year: time.year,
          month: time.month,
          day: time.day,
          count: 0
        };
      }
      resultMap[`${time.year}_${time.month}_${time.day}`].count += 1;
    }

    return Object.values(resultMap);
  }

  async getBillList(
    req: Request,
    reqUser: IReqUser,
    query: QueryInspectionBillListDto
  ): Promise<InspectionBillListResponseDto[]> {
    const inspectionBills: InspectionBillListResponseDto[] =
      await this.repository.selectBillList(reqUser, query);

    const orgIds = new Set([reqUser.orgId]);
    const supplierIds = new Set<string>();
    for (const inspectionBill of inspectionBills) {
      if (
        inspectionBill.purchaseType === PurchaseType.TRANSFER_IN &&
        inspectionBill.supplierId
      ) {
        orgIds.add(inspectionBill.supplierId);
      } else if (inspectionBill.supplierId) {
        supplierIds.add(inspectionBill.supplierId);
      }
    }

    const [orgs, suppliers, businessInfo] = await Promise.all([
      this.platformService.getOrgs(req, reqUser.tenantId, Array.from(orgIds)),
      this.prisma.supplierDirectory.findMany({
        select: {
          id: true,
          fullName: true,
          simpleName: true
        },
        where: {
          tenantId: reqUser.tenantId,
          isDeleted: false,
          id: { in: Array.from(supplierIds) }
        }
      }),
      this.prisma.businessBaseInfo.findMany({
        select: {
          id: true,
          companyVersion: true
        },
        where: {
          tenantId: reqUser.tenantId,
          isDeleted: false,
          id: { in: Array.from(supplierIds) }
        }
      })
    ]);
    const orgMap = orgs.reduce((acc: any, cur: any) => {
      acc[cur.id] = cur;
      return acc;
    }, {});
    const supplierMap = suppliers.reduce((acc: any, cur: any) => {
      acc[cur.id] = cur;
      return acc;
    }, {});
    const businessInfoMap = businessInfo.reduce((acc: any, cur: any) => {
      acc[cur.id] = cur;
      return acc;
    }, {});

    const orgName = orgMap[reqUser.orgId]?.sealName;
    for (const inspectionBill of inspectionBills) {
      // 供应商名称
      if (inspectionBill.supplierId) {
        if (inspectionBill.purchaseType === PurchaseType.TRANSFER_IN) {
          // 项目合同章名称
          inspectionBill.supplierName =
            orgMap[inspectionBill.supplierId]?.sealName;
        } else if (
          inspectionBill.purchaseType !== PurchaseType.PARTY_A_SUPPLIED
        ) {
          // 供应商名称
          inspectionBill.supplierName =
            supplierMap[inspectionBill.supplierId]?.simpleName ||
            businessInfoMap[inspectionBill.supplierId]?.companyVersion;
        } else {
          // 甲供类型，默认取建设单位名称
          inspectionBill.supplierName = inspectionBill.supplierOldName;
        }
      }
      // 项目名称
      inspectionBill.orgName = orgName;
    }

    return inspectionBills;
  }

  async addInspectionBill(
    reqUser: IReqUser
  ): Promise<InspectionBillListResponseDto> {
    // 获取进场时间
    const dateNow = dayjs();
    const year = dateNow.year();
    const month = dateNow.month() + 1;
    const day = dateNow.date();

    // 生成单据编码
    const code = await this.generateInspectionBillCode(reqUser, year, month);

    const data: InspectionBillListResponseDto =
      await this.prisma.materialIncomingInspection.create({
        data: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          creator: reqUser.nickname,
          createBy: reqUser.id,
          updateBy: reqUser.id,
          code,
          purchaseType: PurchaseType.SELF_PURCHASE,
          year,
          month,
          day
        },
        select: {
          materialReceiptStatus: true,
          id: true,
          orgId: true,
          code: true,
          purchaseType: true,
          creator: true,
          year: true,
          month: true,
          day: true,
          submitStatus: true,
          auditStatus: true,
          contractId: true,
          contractName: true,
          supplierId: true,
          supplierName: true
        }
      });

    const orgInfo = await this.prisma.$queryRaw<any[]>`
      select name from platform_meta.org where tenant_id = ${reqUser.tenantId} and id = ${data.orgId}
    `;
    data.orgName = orgInfo[0]?.name || '';
    return data;
  }

  private async generateInspectionBillCode(
    reqUser: IReqUser,
    year: number,
    month: number,
    id?: string
  ): Promise<string> {
    const code = ['验', `${year}${String(month).padStart(2, '0')}`, '001'];
    const lastInspectionBill =
      await this.prisma.materialIncomingInspection.findFirst({
        where: Object.assign(
          {
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            isDeleted: false,
            year,
            month
          },
          id ? { id: { not: id } } : {}
        ),
        orderBy: {
          code: 'desc'
        }
      });

    if (lastInspectionBill) {
      const lastCode = lastInspectionBill.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }

    return code.join('-');
  }

  async editInspectionBill(
    req: Request,
    reqUser: IReqUser,
    data: UpdateInspectionBillListDto
  ) {
    const oldInspectionBill =
      await this.prisma.materialIncomingInspection.findUnique({
        where: {
          id: data.id,
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId
        },
        select: {
          contractId: true,
          purchaseType: true,
          submitStatus: true,
          auditStatus: true,
          year: true,
          month: true,
          day: true
        }
      });

    if (!oldInspectionBill) return;

    // 审批状态变为已审批且被收料单引用后，不可以修改为未审批
    if (
      data.auditStatus &&
      oldInspectionBill.auditStatus === AuditStatus.APPROVED &&
      data.auditStatus !== AuditStatus.APPROVED
    ) {
      const result = await this.repository.selectReceivedInspectionBillIds(
        reqUser.tenantId,
        reqUser.orgId,
        data.id
      );
      if (result.length) {
        throw new BadRequestException(
          `审批通过且被收料单引用的进场验收单，不能修改审批状态为: ${AuditStatusText[data.auditStatus]}`
        );
      }
    }

    // 是否修改了进场日期
    if (
      data.year &&
      data.month &&
      data.day &&
      (data.year !== oldInspectionBill.year ||
        data.month !== oldInspectionBill.month ||
        data.day !== oldInspectionBill.day)
    ) {
      //有合同， 校验进场时间不能小于原始合同的编制时间
      if (oldInspectionBill.contractId) {
        const contract = await this.prisma.materialContract.findFirst({
          where: {
            id: oldInspectionBill.contractId,
            isDeleted: false,
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId
          },
          select: {
            signDate: true
          }
        });
        if (
          contract &&
          dayjs(contract.signDate).isAfter(
            dayjs(`${data.year}-${data.month}-${data.day}`)
          )
        ) {
          throw new BadRequestException('进场时间不能小于合同签订时间!');
        }
      }

      // 改进场日期要重新生成编码
      (data as any).code = await this.generateInspectionBillCode(
        reqUser,
        data.year,
        data.month,
        data.id
      );
    }

    await this.prisma.$transaction(async (t) => {
      // 更新进场验收单数据
      await t.materialIncomingInspection.update({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          id: data.id
        },
        data: {
          ...data,
          updateBy: reqUser.id,
          updateAt: new Date()
        }
      });

      // 如果采购类型发生了变化，清掉明细的数据
      if (oldInspectionBill.purchaseType !== data.purchaseType) {
        t.materialIncomingInspectionDetail.updateMany({
          where: {
            tenantId: reqUser.tenantId,
            orgId: reqUser.orgId,
            isDeleted: false,
            incomingInspectionId: data.id
          },
          data: {
            isDeleted: true,
            updateBy: reqUser.id,
            updateAt: new Date()
          }
        });
      }
    });

    // 如果提交状态发生了改变
    if (oldInspectionBill.submitStatus !== data.submitStatus) {
      // 提交时异步生成excel文件
      if (data.submitStatus === SubmitStatus.SUBMITTED) {
        this.generateExcelFile(req, reqUser, data.id);
      }
    }

    return true;
  }

  async generateExcelFile(req: Request, reqUser: IReqUser, id: string) {
    // 先把excel旧的excel文件地址置空
    await this.prisma.materialIncomingInspection.update({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      },
      data: { excelFileKey: null }
    });
    // 获取进场验收单数据
    const billInfo = await this.repository.getBillInfoById(
      reqUser.tenantId,
      reqUser.orgId,
      id
    );

    const bodyData: Record<string, any> = {
      id,
      orgName: billInfo.orgName,
      supplierName: billInfo.supplierName,
      inspectionBillCode: billInfo.code,
      siteEntryDate: dayjs(
        `${billInfo.year}-${String(billInfo.month).padStart(2, '0')}-${String(billInfo.day).padStart(2, '0')}`
      ).format('YYYY-MM-DD'),
      type: 'materialIncomingInspectionExportA4'
    };
    const { taskId } = await this.excelService.exportExcel(req, bodyData);
    if (!taskId) {
      throw new InternalServerErrorException('excel导出获取taskId失败');
    }

    // 使用 Promise 轮询获取 Excel 导出进度
    const data = await this.excelService.scheduleGetProgress(req, taskId);

    // 保存生成的excel地址
    await this.prisma.materialIncomingInspection.update({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      },
      data: {
        excelFileKey: data.fileKey,
        qrCodeUrl: data.qrCodeUrl
      }
    });
  }

  async deleteInspectionBill(reqUser: IReqUser, id: string) {
    // 判断是否已提交
    const inspectionBill =
      await this.prisma.materialIncomingInspection.findFirst({
        where: {
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          id
        },
        select: {
          id: true,
          submitStatus: true,
          auditStatus: true
        }
      });
    if (inspectionBill?.submitStatus === SubmitStatus.SUBMITTED) {
      throw new BadRequestException('已提交单据，不能删除!');
    }

    await this.prisma.$transaction([
      this.prisma.materialIncomingInspection.update({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          id
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id,
          updateAt: new Date()
        }
      }),
      this.prisma.materialIncomingInspectionDetail.updateMany({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          incomingInspectionId: id
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id,
          updateAt: new Date()
        }
      }),
      this.prisma.materialIncomingInspectionAttachment.updateMany({
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          incomingInspectionId: id
        },
        data: {
          isDeleted: true,
          updateBy: reqUser.id,
          updateAt: new Date()
        }
      })
    ]);

    return true;
  }

  // 进场验收单查询导出数据接口
  async getExportSheetData(reqUser: IReqUser, inspectionBillId: string) {
    const result: any[] =
      await this.prisma.materialIncomingInspectionDetail.findMany({
        select: {
          id: true,
          materialId: true,
          materialName: true,
          materialSpec: true,
          qualityStandard: true,
          unit: true,
          siteEntryQuantity: true,
          actualQuantity: true,
          appearanceDescription: true,
          remark: true
        },
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          incomingInspectionId: inspectionBillId
        },
        orderBy: { orderNo: 'asc' }
      });

    for (const item of result) {
      item.siteEntryQuantity = Decimal(item.siteEntryQuantity)
        .toFixed(2)
        .toString();
      item.actualQuantity = Decimal(item.actualQuantity).toFixed(2).toString();
    }

    return { records: result };
  }
}
