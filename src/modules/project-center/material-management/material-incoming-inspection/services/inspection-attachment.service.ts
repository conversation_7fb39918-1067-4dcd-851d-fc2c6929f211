import { BadRequestException, Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { SubmitStatus } from '@/prisma/generated';

import {
  CreateInspectionAttachmentDto,
  InspectionAttachmentResponseDto
} from '../material-incoming-inspection.dto';

@Injectable()
export class MaterialIncomingInspectionAttachmentService {
  constructor(private readonly prisma: PrismaService) {}

  // 添加附件
  async addInspectionAttachment(
    reqUser: IReqUser,
    data: CreateInspectionAttachmentDto
  ) {
    // 判断是否已提交
    const inspectionBill =
      await this.prisma.materialIncomingInspection.findFirst({
        where: {
          isDeleted: false,
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          id: data.incomingInspectionId
        },
        select: {
          id: true,
          submitStatus: true,
          auditStatus: true
        }
      });
    if (inspectionBill?.submitStatus === SubmitStatus.SUBMITTED) {
      throw new BadRequestException('已提交单据，不能新增附件');
    }

    await this.prisma.materialIncomingInspectionAttachment.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  // 获取附件列表
  async getInspectionAttachmentList(
    reqUser: IReqUser,
    inspectionBillId: string
  ): Promise<InspectionAttachmentResponseDto[]> {
    return await this.prisma.materialIncomingInspectionAttachment.findMany({
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false,
        incomingInspectionId: inspectionBillId
      },
      select: {
        id: true,
        incomingInspectionId: true,
        fileName: true,
        fileKey: true,
        fileSize: true,
        fileExt: true,
        fileContentType: true
      }
    });
  }

  // 删除附件
  async deleteInspectionAttachment(reqUser: IReqUser, fileKey: string) {
    // // 判断是否已提交
    // const inspectionBill =
    //   await this.prisma.materialIncomingInspection.findFirst({
    //     where: {
    //       isDeleted: false,
    //       tenantId: reqUser.tenantId,
    //       orgId: reqUser.orgId,
    //       id
    //     },
    //     select: {
    //       id: true,
    //       submitStatus: true,
    //       auditStatus: true
    //     }
    //   });
    // if (inspectionBill?.submitStatus === SubmitStatus.SUBMITTED) {
    //   throw new BadRequestException('已提交单据，不能删除附件');
    // }

    await this.prisma.materialIncomingInspectionAttachment.updateMany({
      data: {
        isDeleted: true,
        updateBy: reqUser.id,
        updateAt: new Date()
      },
      where: {
        fileKey,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        orgId: reqUser.orgId
      }
    });
    return true;
  }
}
