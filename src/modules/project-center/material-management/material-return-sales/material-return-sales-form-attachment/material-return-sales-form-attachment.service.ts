import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';

import { MaterialReturnSalesAttachmentCreateDto } from './material-return-sales-form-attachment.dto';

@Injectable()
export class MaterialReturnSalesFormAttachmentService {
  constructor(private readonly prisma: PrismaService) {}

  async getList(returnSalesFormId: string, reqUser: IReqUser) {
    return await this.prisma.materialReturnSalesFormAttachment.findMany({
      where: {
        returnSalesFormId,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      }
    });
  }

  async add(data: MaterialReturnSalesAttachmentCreateDto, reqUser: IReqUser) {
    return await this.prisma.materialReturnSalesFormAttachment.create({
      data: {
        ...data,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        createBy: reqUser.id
      }
    });
  }

  async delete(fileKey: string, reqUser: IReqUser) {
    return await this.prisma.materialReturnSalesFormAttachment.updateMany({
      where: {
        fileKey,
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId
      },
      data: {
        isDeleted: true,
        updateBy: reqUser.id
      }
    });
  }
}
