import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ReqUser } from '@/common/decorators/req-user.decorator';
import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { MaterialReturnSalesForm } from '@/prisma/generated';

import {
  MaterialReceivingContractResDto,
  MaterialReturnSalesFormResDto,
  MaterialReturnSalesFormUpdateAuditStatusDto,
  MaterialReturnSalesFormUpdateDto,
  MaterialReturnSalesFormUpdateSubmitStatusDto,
  QueryMaterialReturnSalesFormDto,
  QueryReceivingContractDto,
  QueryReceivingSupplierDto
} from './material-return-sales-form.dto';
import { MaterialReturnSalesFormService } from './material-return-sales-form.service';

@ApiTags('退货单/列表')
@Controller('material-return-sales-form')
export class MaterialReturnSalesFormController {
  constructor(private readonly service: MaterialReturnSalesFormService) {}

  @ApiOperation({ summary: '收料单下所有的合同列表' })
  @ApiResponse({
    status: 200,
    description: '获取收料单下所有的合同列表成功',
    type: MaterialReceivingContractResDto,
    isArray: true
  })
  @Get('/contract-list')
  async getContractList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryReceivingContractDto
  ): Promise<MaterialReceivingContractResDto[]> {
    return await this.service.getContractList(reqUser, query);
  }

  @ApiOperation({ summary: '收料单下所有的供应商列表' })
  @ApiResponse({
    status: 200,
    description: '获取收料单下所有的供应商列表成功',
    type: MaterialReceivingContractResDto,
    isArray: true
  })
  @Get('/supplier-list')
  async getSupplierList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryReceivingSupplierDto
  ): Promise<MaterialReceivingContractResDto[]> {
    return await this.service.getSupplierList(reqUser, query);
  }

  @ApiOperation({ summary: '新增退货单' })
  @ApiResponse({
    status: 200,
    description: '新增退货单成功'
  })
  @Post()
  async add(@ReqUser() reqUser: IReqUser): Promise<MaterialReturnSalesForm> {
    return await this.service.add(reqUser);
  }

  @ApiOperation({ summary: '编辑退货单' })
  @ApiResponse({
    status: 200,
    description: '编辑退货单成功'
  })
  @Patch('/:id')
  async update(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() data: MaterialReturnSalesFormUpdateDto
  ): Promise<boolean> {
    return await this.service.update(id, reqUser, data);
  }

  @ApiOperation({ summary: '退货单左侧树' })
  @ApiResponse({
    status: 200,
    description: '获取退货单左侧树成功',
    type: TimeListResponseDto,
    isArray: true
  })
  @Get('/date-tree')
  async getDateTree(@ReqUser() reqUser: IReqUser) {
    return await this.service.getDateTree(reqUser);
  }

  @ApiOperation({ summary: '退货单/列表' })
  @ApiResponse({
    status: 200,
    description: '获取退货单/列表成功',
    type: MaterialReturnSalesFormResDto,
    isArray: true
  })
  @Get()
  async getList(
    @ReqUser() reqUser: IReqUser,
    @Query() query: QueryMaterialReturnSalesFormDto
  ) {
    return await this.service.getList(reqUser, query);
  }

  @ApiOperation({ summary: '删除退货单' })
  @ApiResponse({
    status: 200,
    description: '删除退货单成功'
  })
  @Delete('/:id')
  async delete(
    @ReqUser() reqUser: IReqUser,
    @Param('id') id: string
  ): Promise<boolean> {
    return await this.service.delete(id, reqUser);
  }

  @ApiOperation({ summary: '提交状态变更' })
  @ApiResponse({
    status: 200,
    description: '提交状态变更成功'
  })
  @Patch('/submit/:id')
  async updateSubmitStatus(
    @Req() req: Request,
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialReturnSalesFormUpdateSubmitStatusDto
  ) {
    return await this.service.updateSubmitStatus(req, id, reqUser, body);
  }

  @ApiOperation({ summary: '审核状态变更' })
  @ApiResponse({
    status: 200,
    description: '审核状态变更成功'
  })
  @Patch('/audit/:id')
  async updateAuditStatus(
    @Param('id') id: string,
    @ReqUser() reqUser: IReqUser,
    @Body() body: MaterialReturnSalesFormUpdateAuditStatusDto
  ) {
    const { auditStatus } = body;
    return await this.service.updateAuditStatus(id, reqUser, auditStatus);
  }

  @ApiOperation({
    summary: 'excel导出数据接口',
    description: 'excel导出数据接口'
  })
  @ApiResponse({ status: 200, description: '查询导出数据成功' })
  @Get('/detail/excel/export-data')
  async getExcelSheetData(
    @ReqUser() reqUser: IReqUser,
    @Query('id') returnSalesFormId: string,
    @Query('isA4Page') isA4Page: boolean
  ) {
    return await this.service.getExportSheetData(
      reqUser,
      returnSalesFormId,
      isA4Page
    );
  }
}
