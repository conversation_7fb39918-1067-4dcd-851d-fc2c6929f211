import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException
} from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import * as dayjs from 'dayjs';

import { TimeListResponseDto } from '@/common/dtos/common.dto';
import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import { ExcelService } from '@/modules/platform/excel.service';
import {
  AuditStatus,
  MaterialReturnSalesForm,
  Prisma,
  SettlementBillType,
  SubmitStatus
} from '@/prisma/generated';

import { MaterialReturnSalesFormDetailService } from '../material-return-sales-form-detail/material-return-sales-form-detail.service';
import {
  MaterialReceivingContractResDto,
  MaterialReturnSalesFormResDto,
  MaterialReturnSalesFormUpdateDto,
  MaterialReturnSalesFormUpdateSubmitStatusDto,
  QueryMaterialReturnSalesFormDto,
  QueryReceivingContractDto,
  QueryReceivingSupplierDto
} from './material-return-sales-form.dto';
import { MaterialReturnSalesFormRepository } from './material-return-sales-form.repositories';

@Injectable()
export class MaterialReturnSalesFormService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly excelService: ExcelService,
    private readonly repository: MaterialReturnSalesFormRepository,
    private readonly materialReturnSalesFormDetailService: MaterialReturnSalesFormDetailService
  ) {}

  /**
   * 查询收料单下的合同
   * @param reqUser
   * @param query
   * @returns
   */
  async getContractList(
    reqUser: IReqUser,
    query: QueryReceivingContractDto
  ): Promise<MaterialReceivingContractResDto[]> {
    const { tenantId, orgId } = reqUser;
    const { supplierId } = query;
    let contract = await this.prisma.materialReceiving.findMany({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        submitStatus: 'SUBMITTED',
        auditStatus: 'APPROVED',
        supplierId: supplierId ? supplierId : undefined,
        contractId: {
          gt: ''
        },
        purchaseType: {
          in: ['SELF_PURCHASE', 'CENTRALIZED_PURCHASE', 'PARTY_A_DIRECTED']
        }
      },
      distinct: ['contractId'],
      select: {
        purchaseType: true,
        supplierId: true,
        contractId: true
      }
    });
    const contractIds: string[] = contract.map((item) => item.contractId!);
    const supplierIds: string[] = contract.map((item) => item.supplierId!);
    // 获取合同名称
    contract = await this.getContractName(contractIds, reqUser, contract);
    contract = await this.getSupplierNameForSelect(
      supplierIds,
      reqUser,
      contract
    );
    return contract;
  }

  async getSupplierName(
    supplierIds: string[],
    reqUser: IReqUser,
    contract: any[]
  ) {
    supplierIds = supplierIds.filter((item) => item !== null && item !== '');
    const { tenantId } = reqUser;
    // 获取供应商信息
    const supplierList = await this.prisma.supplierDirectory.findMany({
      where: {
        id: {
          in: supplierIds
        },
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        simpleName: true
      }
    });
    // 获取公司基本信息
    const companyInfoList = await this.prisma.businessBaseInfo.findMany({
      where: {
        id: {
          in: supplierIds
        },
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        companyVersion: true
      }
    });
    const partAInfoList: Record<string, any>[] = await this.prisma.$queryRaw`
      select
        base_field.id
        ,base_ledger.value
      from basic_project_info_field_detail as base_field
      join basic_project_info_ledger as base_ledger
        on base_ledger.is_deleted = false
        and base_ledger.org_id = ${reqUser.orgId}
        and base_ledger.basic_project_info_field_detail_id = base_field.id
      where base_field.is_deleted = false
        ${supplierIds.length ? Prisma.sql`and base_field.id in (${Prisma.join(supplierIds)})` : Prisma.empty}
    `;
    return contract.map((item) => {
      const supplier = supplierList.find((sup) => sup.id === item.supplierId);
      const companyInfo = companyInfoList.find(
        (company) => company.id === item.supplierId
      );
      const partAInfo = partAInfoList.find(
        (partA) => partA.id === item.supplierId
      );
      if (supplier) {
        return {
          ...item,
          supplierName: supplier?.simpleName
        };
      }
      if (partAInfo) {
        return {
          ...item,
          supplierName: partAInfo?.value
        };
      }
      if (companyInfo) {
        return {
          ...item,
          supplierName: companyInfo?.companyVersion
        };
      }
      if (!supplier && !companyInfo && !partAInfo) {
        return {
          ...item
        };
      }
    });
  }

  async getContractName(
    contractIds: string[],
    reqUser: IReqUser,
    contract: any[]
  ) {
    const { orgId, tenantId } = reqUser;
    const contractList = await this.prisma.materialContract.findMany({
      where: {
        id: {
          in: contractIds
        },
        orgId,
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        name: true
      }
    });
    return contract.map((item) => {
      const con = contractList.find((con) => con.id === item.contractId);
      return {
        ...item,
        contractName: con?.name
      };
    });
  }

  /**
   * 查询收料单下的供应商
   * @param reqUser
   * @param query
   * @returns
   */
  async getSupplierList(
    reqUser: IReqUser,
    query: QueryReceivingSupplierDto
  ): Promise<MaterialReceivingContractResDto[]> {
    const { tenantId, orgId } = reqUser;
    const { contractId } = query;
    let supplier = await this.prisma.materialReceiving.findMany({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        submitStatus: 'SUBMITTED',
        auditStatus: 'APPROVED',
        contractId: contractId ? contractId : undefined,
        purchaseType: {
          in: ['SELF_PURCHASE', 'CENTRALIZED_PURCHASE', 'PARTY_A_DIRECTED']
        }
      },
      distinct: ['supplierId'],
      select: {
        purchaseType: true,
        supplierId: true,
        supplierName: true,
        contractId: true,
        contractName: true
      }
    });
    const contractIds: string[] = supplier.map((item) => item.contractId!);
    const supplierIds: string[] = supplier.map((item) => item.supplierId!);
    // 获取合同名称
    supplier = await this.getContractName(contractIds, reqUser, supplier);
    supplier = await this.getSupplierNameForSelect(
      supplierIds,
      reqUser,
      supplier
    );
    return supplier;
  }

  async getSupplierNameForSelect(
    supplierIds: string[],
    reqUser: IReqUser,
    contract: any[]
  ) {
    supplierIds = supplierIds.filter((item) => item !== null && item !== '');
    const { tenantId } = reqUser;
    // 获取供应商信息
    const supplierList = await this.prisma.materialContract.findMany({
      where: {
        partyB: {
          in: supplierIds
        },
        tenantId,
        isDeleted: false
      },
      select: {
        id: true,
        partyBEndName: true
      }
    });
    const partAInfoList: Record<string, any>[] = await this.prisma.$queryRaw`
      select
        base_field.id
        ,base_ledger.value
      from basic_project_info_field_detail as base_field
      join basic_project_info_ledger as base_ledger
        on base_ledger.is_deleted = false
        and base_ledger.org_id = ${reqUser.orgId}
        and base_ledger.basic_project_info_field_detail_id = base_field.id
      where base_field.is_deleted = false
        ${supplierIds.length ? Prisma.sql`and base_field.id in (${Prisma.join(supplierIds)})` : Prisma.empty}
    `;
    return contract.map((item) => {
      const supplier = supplierList.find((sup) => sup.id === item.supplierId);
      const partAInfo = partAInfoList.find(
        (partA) => partA.id === item.supplierId
      );
      if (supplier) {
        return {
          ...item,
          supplierName: supplier?.partyBEndName
        };
      }
      if (partAInfo) {
        return {
          ...item,
          supplierName: partAInfo?.value
        };
      }
      if (!supplier && !partAInfo) {
        return {
          ...item
        };
      }
    });
  }

  /**
   * 新增退货单
   * @param req
   * @param reqUser
   */
  async add(reqUser: IReqUser): Promise<MaterialReturnSalesForm> {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const day = currentDate.getDate();
    // 获取当退货单的最新的退货单编号
    const code = await this.getMaxCode(reqUser, year, month);
    const data = await this.prisma.materialReturnSalesForm.create({
      data: {
        year: year,
        month: month,
        day: day,
        code: code,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        createBy: reqUser.id,
        updateBy: reqUser.id
      }
    });
    return await this.repository.getOne(data.id, reqUser);
  }

  /**
   * 编辑退货单
   * @param id
   * @param reqUser
   * @param data
   * @returns
   */
  async update(
    id: string,
    reqUser: IReqUser,
    data: MaterialReturnSalesFormUpdateDto
  ): Promise<boolean> {
    await this.checkUpdateData(data, reqUser);
    // 查询单据退货时间
    const receiving = await this.getOne(id, reqUser);
    if (data.year && data.month && data.month !== receiving.month) {
      data.code = await this.getMaxCode(reqUser, data.year, data.month);
    }
    await this.prisma.materialReturnSalesForm.update({
      where: {
        id,
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false
      },
      data: {
        ...data,
        updateBy: reqUser.id
      }
    });
    return true;
  }

  async checkUpdateData(
    data: MaterialReturnSalesFormUpdateDto,
    reqUser: IReqUser
  ) {
    // 获取退货单下的所有子项
    if (
      data.supplierId &&
      data.contractId &&
      data.year &&
      data.month &&
      data.day
    ) {
      const salesDate = dayjs()
        .year(data.year)
        .month(data.month - 1)
        .date(data.day)
        .format('YYYY-MM-DD')
        .toString();
      // 查询最小时间
      const date =
        await this.materialReturnSalesFormDetailService.getEarliestTime(
          data.supplierId,
          data.contractId,
          reqUser
        );
      if (date) {
        const minDate = dayjs(date)
          .startOf('day')
          .format('YYYY-MM-DD')
          .toString();
        console.log(minDate, salesDate);
        if (salesDate < minDate)
          throw new BadRequestException(
            `退货日期不能大于收料最小日期，收料最小日期：${minDate}`
          );
        return;
      }
    }
  }

  async getDateTree(reqUser: IReqUser): Promise<TimeListResponseDto[]> {
    // 实现获取时间列表的逻辑
    const dates = await this.prisma.materialReturnSalesForm.findMany({
      distinct: ['year', 'month', 'day'],
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        isDeleted: false
      },
      orderBy: [{ year: 'desc' }, { month: 'desc' }, { day: 'desc' }]
    });

    const resultMap: Record<string, TimeListResponseDto> = {};
    for (const time of dates) {
      // 添加父级，年_月
      if (!resultMap[`${time.year}_${time.month}`]) {
        resultMap[`${time.year}_${time.month}`] = {
          id: `${time.year}_${time.month}`,
          count: await this.getDateCount(reqUser, time.year, time.month),
          parentId: null,
          year: time.year,
          month: time.month
        };
      }

      // 添加子级 年_月_日
      resultMap[`${time.year}_${time.month}_${time.day}`] = {
        id: `${time.year}_${time.month}_${time.day}`,
        count: await this.getDateCount(
          reqUser,
          time.year,
          time.month,
          time.day
        ),
        parentId: `${time.year}_${time.month}`,
        year: time.year,
        month: time.month,
        day: time.day
      };
    }

    return Object.values(resultMap);
  }

  async getList(
    reqUser: IReqUser,
    query: QueryMaterialReturnSalesFormDto
  ): Promise<MaterialReturnSalesFormResDto[]> {
    let list: MaterialReturnSalesFormResDto[] = await this.repository.getList(
      reqUser,
      query
    );
    const supplierIds: string[] = list.map((item) => item.supplierId!);
    // 获取合同名称
    list = await this.getSupplierName(supplierIds, reqUser, list);
    return list;
  }

  /**
   * 删退货单
   * @param id
   * @param reqUser
   */
  async delete(id: string, reqUser: IReqUser) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 删除前校验
    await this.beforeDelete(id, reqUser);
    // 删除退货单必须返还库存
    // 查询退货单下的父级明细
    const parentDetails =
      await this.materialReturnSalesFormDetailService.getParentList(
        id,
        reqUser
      );
    await this.prisma.$transaction(async (tx) => {
      await tx.materialReturnSalesForm.update({
        where: {
          id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
      for (const element of parentDetails) {
        await this.materialReturnSalesFormDetailService.delete(
          element.id,
          reqUser
        );
      }
      await tx.materialReturnSalesFormAttachment.updateMany({
        where: {
          returnSalesFormId: id,
          orgId,
          tenantId,
          isDeleted: false
        },
        data: {
          isDeleted: true,
          updateBy: userId
        }
      });
    });
    return true;
  }

  /**
   * 修改提交状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateSubmitStatus(
    req: Request,
    id: string,
    reqUser: IReqUser,
    body: MaterialReturnSalesFormUpdateSubmitStatusDto
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    const { submitStatus } = body;
    const data: {
      submitStatus: SubmitStatus;
      updateBy: string;
    } = {
      submitStatus,
      updateBy: userId
    };
    // 提交状态变更校验逻辑
    await this.beforeUpdateSubmitStatus(id, reqUser, submitStatus);
    await this.prisma.materialReturnSalesForm.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: data
    });
    if (submitStatus === SubmitStatus.SUBMITTED) {
      this.generateExcelFile(req, reqUser, id, false);
      this.generateExcelFile(req, reqUser, id, true);
    }
    return true;
  }

  /**
   * 修改审批状态
   * @param id
   * @param reqUser
   * @param data
   */
  async updateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const { tenantId, orgId, id: userId } = reqUser;
    // 审批状态变更校验逻辑
    await this.beforeUpdateAuditStatus(id, reqUser, auditStatus);
    await this.prisma.materialReturnSalesForm.update({
      where: {
        id,
        orgId,
        tenantId,
        isDeleted: false
      },
      data: {
        auditStatus,
        updateBy: userId
      }
    });
    return true;
  }

  async beforeUpdateAuditStatus(
    id: string,
    reqUser: IReqUser,
    auditStatus: AuditStatus
  ) {
    const { tenantId, orgId } = reqUser;
    // 查询退货单
    const data = await this.getOne(id, reqUser);
    if (
      (auditStatus === AuditStatus.PENDING ||
        auditStatus === AuditStatus.REJECTED) &&
      data.auditStatus === AuditStatus.APPROVED
    ) {
      // 校验是否被结算单引用
      const MaterialSettlementBillRefDetailData = await this.prisma.$queryRaw<
        any[]
      >`
        select mrfd.id from material_settlement_bill_ref_detail mrfd
        where mrfd.is_deleted = false
        and mrfd.settlement_bill_type::TEXT = ${SettlementBillType.RETURN_SALES}
        and mrfd.bill_id = ${id}
        and mrfd.org_id = ${orgId}
        and mrfd.tenant_id = ${tenantId}
      `;
      if (MaterialSettlementBillRefDetailData.length) {
        throw new BadRequestException(
          `被结算单引用的退货单，不能${auditStatus === AuditStatus.PENDING ? '撤销' : '退回'}`
        );
      }
    }
  }

  /**
   * 提交状态变更校验逻辑
   * @param id
   * @param reqUser
   * @param submitStatus
   */
  async beforeUpdateSubmitStatus(
    id: string,
    reqUser: IReqUser,
    submitStatus: SubmitStatus
  ) {
    if (submitStatus === SubmitStatus.SUBMITTED) {
      // 提交状态校验
      await this.beforeSubmit(id, reqUser);
    } else {
      // 取消提交状态校验
      await this.beforeUnSubmit(id, reqUser);
    }
  }

  async beforeSubmit(id: string, reqUser: IReqUser) {
    // 查询退货单是否有明细
    const detail = await this.materialReturnSalesFormDetailService.getList(
      id,
      reqUser
    );
    if (!detail.length) {
      throw new BadRequestException('退货单没有明细，不能提交');
    }
    // 校验审批单的必填项
    if (detail.find((item) => item.salesReturnQuantity === null)) {
      throw new BadRequestException('存在明细的退货数量为空,请检查');
    }
  }

  async beforeUnSubmit(id: string, reqUser: IReqUser) {
    // 查询退货单
    const materialReturnSalesForm = await this.getOne(id, reqUser);
    if (materialReturnSalesForm.auditStatus === AuditStatus.APPROVED) {
      // 已审批不可取消提交
      throw new BadRequestException('已审批的退货单，不能取消提交');
    }
  }

  /**
   * 删除前校验
   * @param id
   * @param reqUser
   */
  async beforeDelete(id: string, reqUser: IReqUser) {
    // 查询退货单
    const returnSalesForm = await this.getOne(id, reqUser);
    if (returnSalesForm.submitStatus !== SubmitStatus.PENDING) {
      throw new BadRequestException('已提交的退货单，不能删除');
    }
  }

  async getOne(id: string, reqUser: IReqUser) {
    const data = await this.prisma.materialReturnSalesForm.findUnique({
      where: {
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      }
    });
    if (!data) {
      throw new BadRequestException('退货单不存在');
    }
    return data;
  }

  // 获取当前退货单的最新的退货单编号
  async getMaxCode(
    reqUser: IReqUser,
    year: number,
    month: number
  ): Promise<string> {
    const { tenantId, orgId } = reqUser;
    const code = ['退货', `${year}${String(month).padStart(2, '0')}`, '001'];
    const maxCode = await this.prisma.materialReturnSalesForm.findFirst({
      where: {
        orgId,
        tenantId,
        isDeleted: false,
        year,
        month
      },
      orderBy: {
        code: 'desc'
      }
    });
    if (maxCode?.code.split('-')[1] === code[1]) {
      const lastCode = maxCode.code;
      const lastCodeNumber = parseInt(lastCode.split('-')[2], 10);
      code[2] = String(lastCodeNumber + 1).padStart(3, '0');
    }
    return code.join('-');
  }

  // 查询时间下的数据数量
  async getDateCount(
    reqUser: IReqUser,
    year: number,
    month: number,
    day?: number
  ): Promise<number> {
    return await this.prisma.materialReturnSalesForm.count({
      where: {
        orgId: reqUser.orgId,
        tenantId: reqUser.tenantId,
        isDeleted: false,
        year: year,
        month: month,
        day: day ? day : undefined
      }
    });
  }

  async generateExcelFile(
    req: Request,
    reqUser: IReqUser,
    id: string,
    isA4Page: boolean
  ) {
    // 先把excel旧的excel文件地址置空
    await this.prisma.materialReturnSalesForm.update({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      },
      data: isA4Page ? { excelFileKeyA4: null } : { excelFileKey: null }
    });
    // 获取进场验收单数据
    const materialReturnSalesForm = await this.getReturnSalesFormInfoById(
      reqUser.tenantId,
      reqUser.orgId,
      id
    );

    const bodyData: Record<string, any> = {
      id,
      orgName: materialReturnSalesForm.orgName,
      supplierName: materialReturnSalesForm.supplierName,
      returnSalesCode: materialReturnSalesForm.code,
      siteEntryDate: dayjs(
        `${materialReturnSalesForm.year}-${String(materialReturnSalesForm.month).padStart(2, '0')}-${String(materialReturnSalesForm.day).padStart(2, '0')}`
      ).format('YYYY-MM-DD'),
      type: isA4Page
        ? 'materialReturnSalesFormExportA4'
        : 'materialReturnSalesFormExportNeedle',
      isA4Page
    };
    const res = await this.excelService.exportExcel(req, bodyData);
    if (!res.taskId) {
      throw new InternalServerErrorException('excel导出获取taskId失败');
    }

    // 使用 Promise 轮询获取 Excel 导出进度
    const data = await this.excelService.scheduleGetProgress(req, res.taskId);

    // 保存生成的excel地址
    await this.prisma.materialReturnSalesForm.update({
      where: {
        isDeleted: false,
        tenantId: reqUser.tenantId,
        orgId: reqUser.orgId,
        id
      },
      data: isA4Page
        ? {
            excelFileKeyA4: data.fileKey,
            qrCodeUrlA4: data.qrCodeUrl
          }
        : {
            excelFileKey: data.fileKey,
            qrCodeUrl: data.qrCodeUrl
          }
    });
  }

  async getReturnSalesFormInfoById(
    tenantId: string,
    orgId: string,
    id: string
  ) {
    const result = await this.prisma.$queryRaw<any[]>`
        select
          mr.id
          ,mr.code
          ,mr.purchase_type
          ,mr.supplier_id
          ,mr.supplier_name
          ,mr.contract_id
          ,mr.contract_name
          ,u.nickname as creator
          ,mr.year
          ,mr.month
          ,mr.day
          ,mr.submit_status
          ,mr.audit_status
          ,o.name as org_name
        from material_return_sales_form mr
        join platform_meta.user u
          on u.id = mr.create_by
        join platform_meta.org o
          on o.tenant_id = mr.tenant_id
          and o.id = mr.org_id
        where mr.tenant_id = ${tenantId}
          and mr.org_id = ${orgId}
          and mr.id = ${id}
          and mr.is_deleted = false
      `;

    return result[0];
  }

  // 查询导出数据接口
  async getExportSheetData(
    reqUser: IReqUser,
    returnSalesFormId: string,
    isA4Page: boolean
  ) {
    // 每行显示的数据量
    const limit = isA4Page ? 14 : 4;
    const result: any[] =
      await this.prisma.materialReturnSalesFormDetail.findMany({
        select: {
          id: true,
          materialId: true,
          materialName: true,
          materialSpec: true,
          unit: true,
          salesReturnQuantity: true,
          salesReturnPrice: true,
          salesReturnAmount: true,
          remark: true
        },
        where: {
          tenantId: reqUser.tenantId,
          orgId: reqUser.orgId,
          isDeleted: false,
          returnSalesFormId,
          parentId: null
        },
        orderBy: { orderNo: 'asc' }
      });
    let salesReturnAmount = Decimal(0);
    for (const item of result) {
      salesReturnAmount = Decimal(salesReturnAmount).add(
        Decimal(item.salesReturnAmount)
      );
      item.salesReturnPrice = Decimal(item.salesReturnPrice)
        .toFixed(2)
        .toString();
      item.salesReturnAmount = Decimal(item.salesReturnAmount)
        .toFixed(2)
        .toString();
      item.salesReturnQuantity = Decimal(item.salesReturnQuantity)
        .toFixed(2)
        .toString();
    }
    // 总数
    const total = result.length + 1;
    // 余数
    const remainder = total % limit;
    if (remainder !== 0) {
      for (let index = 0; index < limit - remainder; index++) {
        result.push({
          id: '',
          materialId: '',
          materialName: '',
          materialSpec: '',
          unit: '',
          salesReturnQuantity: '',
          salesReturnPrice: '',
          salesReturnAmount: '',
          remark: ''
        });
      }
    }
    result.push({
      id: '',
      materialId: '',
      materialName: '合计',
      materialSpec: '',
      unit: '',
      salesReturnQuantity: '',
      salesReturnPrice: '',
      salesReturnAmount: salesReturnAmount.toFixed(2).toString(),
      remark: ''
    });

    return { records: result };
  }
}
