import { Injectable } from '@nestjs/common';

import { IReqUser } from '@/common/interfaces/req-user.interface';
import { PrismaService } from '@/common/modules/prisma/prisma.service';
import {
  AuditStatus,
  MaterialSettlementStatus,
  Prisma,
  SubmitStatus
} from '@/prisma/generated';
import { Decimal } from '@/prisma/generated/internal/prismaNamespace';

import {
  MaterialReturnSalesFormChooseCategoryTreeResDto,
  MaterialReturnSalesFormChooseDetailsDto,
  MaterialReturnSalesFormDetailCreateListDto
} from './material-return-sales-form-detail.dto';

interface QueryInterface {
  contractId: string;
  supplierId: string;
  materialName: string;
  materialSpec: string;
  unit: string;
  price: number;
}

@Injectable()
export class MaterialReturnSalesFormDetailRepository {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 查询可选的材料分类
   * @param returnSalesFormId
   * @param reqUser
   */
  async getChooseMaterialCategory(
    contractId: string | null,
    supplierId: string | null,
    reqUser: IReqUser,
    returnSalesFormDate: string
  ): Promise<MaterialReturnSalesFormChooseCategoryTreeResDto[]> {
    return await this.prisma.$queryRaw<
      MaterialReturnSalesFormChooseCategoryTreeResDto[]
    >`
        WITH RECURSIVE temp_inventory as (
        -- 查询可用的库存（材料）
          SELECT 
            DISTINCT mdd.material_dictionary_category_id as category_id
          from material_receiving_inventory mri
          join material_dictionary_detail mdd
            on mdd.tenant_id = mri.tenant_id
            and mdd.is_deleted = false
            and mdd.id = mri.material_id
          join material_receiving mr
            on mr.tenant_id = mri.tenant_id
            and mri.receiving_id = mr.id
            and mr.org_id = mri.org_id
            and mr.is_deleted = false
            ${contractId ? Prisma.sql`and mr.contract_id = ${contractId}` : Prisma.empty}
            ${supplierId ? Prisma.sql`and mr.supplier_id = ${supplierId}` : Prisma.empty}
            and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
            and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
            and mr.material_settlement_status::TEXT != ${MaterialSettlementStatus.SETTLED}
            and TO_DATE(CONCAT_WS('-', mr.year, mr.month, mr.day), 'YYYY-MM-DD') <= TO_DATE(${returnSalesFormDate}, 'YYYY-MM-DD')
          where mri.is_deleted = false
            and mri.tenant_id = ${reqUser.tenantId}
            and mri.org_id = ${reqUser.orgId}
            and mri.inventory_quantity > 0
        ), 
        -- 根据材料id去重查询分类
        temp_category  as (
          select 
            id
            ,parent_id
            ,code
            ,name
            ,type
            ,remark
            ,level
            ,sort
            ,tenant_id
          from material_dictionary_category
          where 
            tenant_id = ${reqUser.tenantId}
            and is_deleted = false
            and id in(select category_id from temp_inventory)
          
          union all 
          
          select 
            mdc.id
            ,mdc.parent_id
            ,mdc.code
            ,mdc.name
            ,mdc.type
            ,mdc.remark
            ,mdc.level
            ,mdc.sort
            ,mdc.tenant_id
          from material_dictionary_category mdc
          join temp_category t_c
            on t_c.tenant_id = mdc.tenant_id
            and mdc.id = t_c.parent_id
          where 
            mdc.is_deleted = false
        ),
        tmp_data as(select distinct
            id
            ,parent_id
            ,code
            ,name
            ,type
            ,remark
						,level
						,sort
          from temp_category
				)
				select * from tmp_data
        order by level, sort
    `;
  }

  /**
   * 查询可选的材料明细
   * @param returnSalesFormId
   * @param reqUser
   */
  async getChooseMaterialDetails(
    returnSalesFormDate: string,
    returnSalesFormId: string,
    contractId: string | null,
    supplierId: string | null,
    reqUser: IReqUser,
    categoryId?: string | null
  ) {
    let categoryIds: string[] = [];
    // 查询父级下的所有子级
    if (categoryId) {
      const categoryIdList = await this.prisma.$queryRaw<any[]>`
        WITH RECURSIVE tree AS (
          -- 锚点查询：获取指定ID的父节点
          SELECT 
              id, 
              parent_id
          FROM 
              material_dictionary_category
          WHERE 
              tenant_id = ${reqUser.tenantId}
              AND is_deleted = false
              AND id = ${categoryId}  -- 指定的节点ID

          UNION ALL

          -- 递归查询：获取所有子级（包括多级子节点）
          SELECT 
              m.id, 
              m.parent_id
          FROM 
              material_dictionary_category m
          JOIN 
              tree t ON m.parent_id = t.id  -- 关联父级ID
          WHERE 
              m.tenant_id = ${reqUser.tenantId}
              AND m.is_deleted = false
      )
      -- 最终查询：返回所有节点（父节点+所有子级）
      SELECT 
        distinct id
      FROM 
          tree
      `;
      categoryIds = categoryIdList.map((item) => item.id);
    }
    return await this.prisma.$queryRaw<
      MaterialReturnSalesFormChooseDetailsDto[]
    >`
        -- 查询可用的库存（材料）
        SELECT 
          mri.id
          ,mri.receiving_id
          ,mri.material_id
          ,mri.material_name
          ,mri.material_spec
          ,mri.unit
          ,mri.price
          ,mri.inventory_quantity
          ,mdd.material_dictionary_category_id as category_id
          ,EXISTS(
						select id from material_return_sales_form_detail 
						where is_deleted = false
            and return_sales_form_id = ${returnSalesFormId}
						and tenant_id = ${reqUser.tenantId}
						and org_id = ${reqUser.orgId}
						and material_name = mri.material_name
						and material_spec = mri.material_spec
						and unit = mri.unit
						and in_stock_price = mri.price
					) as disabled
        from material_receiving_inventory mri
        join material_dictionary_detail mdd
          on mdd.tenant_id = mri.tenant_id
          and mdd.is_deleted = false
          and mdd.id = mri.material_id
        join material_dictionary_category mdc
          on mdc.tenant_id = mri.tenant_id
          ${categoryIds.length ? Prisma.sql`and mdc.id in (${Prisma.join(categoryIds)})` : Prisma.empty}
          and mdc.id = mdd.material_dictionary_category_id
          and mdc.is_deleted = false
        join material_receiving mr
          on mr.tenant_id = mri.tenant_id
          and mr.org_id = mri.org_id
          and mr.is_deleted = false
          ${contractId ? Prisma.sql`and mr.contract_id = ${contractId}` : Prisma.empty}
          ${supplierId ? Prisma.sql`and mr.supplier_id = ${supplierId}` : Prisma.empty}
          and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
          and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
          and mr."id" = mri.receiving_id
          and TO_DATE(CONCAT_WS('-', mr.year, mr.month, mr.day), 'YYYY-MM-DD') <= TO_DATE(${returnSalesFormDate}, 'YYYY-MM-DD')
          and mr.material_settlement_status::TEXT != ${MaterialSettlementStatus.SETTLED}
        where mri.is_deleted = false
          and mri.tenant_id = ${reqUser.tenantId}
          and mri.org_id = ${reqUser.orgId}
          and mri.inventory_quantity > 0
      `;
  }

  // 查询退货单下收料明细的最早时间
  async getEarliestTime(
    supplierId: string,
    contractId: string,
    reqUser: IReqUser
  ): Promise<string | null> {
    const { tenantId, orgId } = reqUser;
    const res = await this.prisma.$queryRaw<any[]>`
      select
        MIN(MAKE_DATE(mr.year, mr.month, mr.day)) AS min_date
        from material_receiving_inventory mri
        join material_receiving mr
          on mr.tenant_id = mri.tenant_id
          and mr.contract_id = ${contractId}
          and mr.supplier_id = ${supplierId}
          and mr.org_id = mri.org_id
          and mr.id = mri.receiving_id
          and mr.is_deleted = false
          and mr.submit_status::TEXT = ${SubmitStatus.SUBMITTED}
          and mr.audit_status::TEXT = ${AuditStatus.APPROVED}
          and mr.material_settlement_status::TEXT != ${MaterialSettlementStatus.SETTLED}
        where 
          mri.tenant_id = ${tenantId}
          and mri.org_id = ${orgId}
          and mri.is_deleted = false
    `;
    return res[0].minDate || null;
  }

  // 查询所有涉及到的材料的收料
  async getMaterialReceivingAndReturnList(
    returnSalesFormDate: string,
    reqUser: IReqUser,
    list: MaterialReturnSalesFormDetailCreateListDto[],
    contractId: string | null,
    supplierId: string | null
  ) {
    const { tenantId, orgId } = reqUser;
    const ml = list.map((m) => {
      // 使用toFixed(6)确保价格保留6位小数，与数据库字段精度匹配
      const formattedPrice = new Decimal(m.inStockPrice).toFixed(6);
      return `${m.materialName}@@${m.materialSpec}@@${m.unit}@@${formattedPrice}`;
    });
    const materialReceivingAndReturnList = await this.prisma.$queryRaw<any[]>`
        SELECT 
          mri.id as inventory_id
          ,mri.material_id
          ,mri.material_name
          ,mri.material_spec
          ,mri.unit
          ,mri.price
          ,mri.inventory_quantity as quantity
          ,mri.receiving_quantity
          ,mr.code
          ,TO_DATE(CONCAT_WS('-', mr.year, mr.month, mr.day), 'YYYY-MM-DD') as create_at
        from material_receiving_inventory mri
        join material_dictionary_detail mdd
          on mdd.tenant_id = mri.tenant_id
          and mdd.is_deleted = false
          and mdd.id = mri.material_id
        join material_receiving mr
          on mr.tenant_id = mri.tenant_id
          and mr.org_id = mri.org_id
          and mr.is_deleted = false
          ${contractId ? Prisma.sql`and mr.contract_id = ${contractId}` : Prisma.empty}
          ${supplierId ? Prisma.sql`and mr.supplier_id = ${supplierId}` : Prisma.empty}
          and mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
          and mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
          and mr.id = mri.receiving_id
          and mr.material_settlement_status::TEXT != ${MaterialSettlementStatus.SETTLED}
          and TO_DATE(CONCAT_WS('-', mr.year, mr.month, mr.day), 'YYYY-MM-DD') <= TO_DATE(${returnSalesFormDate}, 'YYYY-MM-DD')
        where mri.is_deleted = false
          and mri.tenant_id = ${tenantId}
          and mri.org_id = ${orgId}
          and mri.inventory_quantity > 0
          and concat_ws('@@', mri.material_name, mri.material_spec, mri.unit, mri.price) in (
            ${Prisma.join(ml)}
          )
        order by create_at desc
    `;
    return materialReceivingAndReturnList;
  }

  /**
   * 查询现有库存数量
   * @param query
   * @param reqUser
   * @returns
   */
  async getInventory(
    query: QueryInterface,
    reqUser: IReqUser
  ): Promise<number> {
    const { tenantId, orgId } = reqUser;
    const { contractId, supplierId, materialName, materialSpec, unit, price } =
      query;
    const res = await this.prisma.$queryRaw<any[]>`
      SELECT 
          COALESCE(SUM(mri.inventory_quantity), 0) as "total_quantity"
        FROM material_receiving_inventory mri
        JOIN material_dictionary_detail mdd
          ON mdd.tenant_id = mri.tenant_id
          AND mdd.is_deleted = false
          AND mdd.id = mri.material_id
        JOIN material_receiving mr
          ON mr.tenant_id = mri.tenant_id
          AND mr.org_id = mri.org_id
          AND mr.is_deleted = false
          ${contractId ? Prisma.sql`AND mr.contract_id = ${contractId}` : Prisma.empty}
          ${supplierId ? Prisma.sql`AND mr.supplier_id = ${supplierId}` : Prisma.empty}
          AND mr.submit_status = ${SubmitStatus.SUBMITTED}::"SubmitStatus"
          AND mr.audit_status = ${AuditStatus.APPROVED}::"AuditStatus"
          AND mr.id = mri.receiving_id
          and mr.material_settlement_status::TEXT != ${MaterialSettlementStatus.SETTLED}
        WHERE mri.is_deleted = false
          AND mri.tenant_id = ${tenantId}
          AND mri.org_id = ${orgId}
          AND mri.inventory_quantity > 0
          AND mri.material_name = ${materialName}
          AND mri.material_spec = ${materialSpec}
          AND mri.unit = ${unit}
          AND mri.price = ${Decimal(price)}
    `;
    return res[0].totalQuantity;
  }
}
